# 数据库模式与安全策略 (DATABASE_SCHEMA.MD)

本文档是项目的“单一事实来源”，详细定义了所有数据库表的结构、约束以及行级安全策略（Row Level Security, RLS）。所有数据库的变更都应首先在此文档中进行规划。

---

## 核心设计原则

- **主键 (Primary Key)**: 统一使用 `uuid` 类型，以便与 `auth.users` 表的 `id` 进行关联，并避免暴露业务数据量。
- **时间戳 (Timestamp)**: 统一使用 `timestamptz` (`timestamp with time zone`)，以适应全球化需求，避免时区混淆。
- **外键约束 (Foreign Key)**: 明确使用 `references` 定义外键，并通过 `on delete cascade` 实现级联删除，确保数据完整性。
- **行级安全 (RLS)**: 所有需要进行数据隔离的表都必须启用 RLS，并制定严格的策略。策略的核心是利用 Supabase 提供的 `auth.uid()` 函数来验证用户身份。

---

## 1. `profiles` (用户信息表)

关联 `auth.users` 表，存储用户的公开信息。

```sql
-- 1. 创建 profiles 表
CREATE TABLE public.profiles (
  id uuid NOT NULL PRIMARY KEY, -- 关联 auth.users.id
  username text UNIQUE NOT NULL,
  avatar_url text,
  created_at timestamptz DEFAULT now(),

  CONSTRAINT fk_auth_users FOREIGN KEY (id) REFERENCES auth.users(id) ON DELETE CASCADE
);

-- 2. 为 profiles 表启用行级安全 (RLS)
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;

-- 3. 创建 RLS 策略
CREATE POLICY "Public profiles are viewable by everyone."
  ON public.profiles FOR SELECT
  USING (true);

CREATE POLICY "Users can insert their own profile."
  ON public.profiles FOR INSERT
  WITH CHECK (auth.uid() = id);

CREATE POLICY "Users can update their own profile."
  ON public.profiles FOR UPDATE
  USING (auth.uid() = id)
  WITH CHECK (auth.uid() = id);
```

---

## 2. `journals` (日记表)

存储用户的私密日记，具有最严格的隐私策略。

```sql
-- 1. 创建 journals 表
CREATE TABLE public.journals (
  id uuid NOT NULL PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL,
  content text,
  mood text CHECK (mood IN ('happy', 'calm', 'sad', 'neutral', 'anxious')), -- 使用 CHECK 约束保证数据有效性
  created_at timestamptz DEFAULT now(),

  CONSTRAINT fk_profiles FOREIGN KEY (user_id) REFERENCES public.profiles(id) ON DELETE CASCADE
);

-- 2. 为 journals 表启用 RLS
ALTER TABLE public.journals ENABLE ROW LEVEL SECURITY;

-- 3. 创建 RLS 策略
CREATE POLICY "Users can manage their own journals."
  ON public.journals FOR ALL
  USING (auth.uid() = user_id)
  WITH CHECK (auth.uid() = user_id);
```

---

## 3. `posts` (社区帖子表)

存储社区中的公开帖子。

```sql
-- 1. 创建 posts 表
CREATE TABLE public.posts (
  id uuid NOT NULL PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL,
  title text NOT NULL,
  content text,
  created_at timestamptz DEFAULT now(),

  CONSTRAINT fk_profiles FOREIGN KEY (user_id) REFERENCES public.profiles(id) ON DELETE CASCADE
);

-- 2. 为 posts 表启用 RLS
ALTER TABLE public.posts ENABLE ROW LEVEL SECURITY;

-- 3. 创建 RLS 策略
CREATE POLICY "Posts are viewable by everyone." ON public.posts FOR SELECT USING (true);
CREATE POLICY "Users can create their own posts." ON public.posts FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update their own posts." ON public.posts FOR UPDATE USING (auth.uid() = user_id) WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can delete their own posts." ON public.posts FOR DELETE USING (auth.uid() = user_id);
```

---

## 4. `comments` (帖子评论表)

存储对帖子的公开评论。

```sql
-- 1. 创建 comments 表
CREATE TABLE public.comments (
  id uuid NOT NULL PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL,
  post_id uuid NOT NULL,
  content text,
  created_at timestamptz DEFAULT now(),

  CONSTRAINT fk_profiles FOREIGN KEY (user_id) REFERENCES public.profiles(id) ON DELETE CASCADE,
  CONSTRAINT fk_posts FOREIGN KEY (post_id) REFERENCES public.posts(id) ON DELETE CASCADE
);

-- 2. 为 comments 表启用 RLS
ALTER TABLE public.comments ENABLE ROW LEVEL SECURITY;

-- 3. 创建 RLS 策略
CREATE POLICY "Comments are viewable by everyone." ON public.comments FOR SELECT USING (true);
CREATE POLICY "Users can create their own comments." ON public.comments FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update their own comments." ON public.comments FOR UPDATE USING (auth.uid() = user_id) WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can delete their own comments." ON public.comments FOR DELETE USING (auth.uid() = user_id);
```

---

## 5. 自动化：新用户 Profile 创建

通过数据库触发器，实现新用户注册时自动在 `profiles` 表中创建关联记录。

```sql
-- 1. 创建一个函数，用于在 public.profiles 中插入新行
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS trigger
LANGUAGE plpgsql
SECURITY DEFINER -- 关键：使用创建者的权限执行
AS $$
BEGIN
  INSERT INTO public.profiles (id, username)
  VALUES (new.id, new.raw_user_meta_data->>'username'); -- 从注册元数据中获取用户名
  RETURN new;
END;
$$;

-- 2. 创建一个触发器，在 auth.users 表有新用户插入后，调用上述函数
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE PROCEDURE public.handle_new_user();
```

**注意**: 此触发器依赖于在前端注册时，通过 `options.data` 传递 `username` 字段。