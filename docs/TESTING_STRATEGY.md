# 详细测试策略

**文档状态**: 草案
**所有者**: QA负责人/技术负责人

---

## 1. 目的

本文件旨在为 Upbase 应用制定一个全面、多层次的测试策略，以确保产品的功能正确性、性能稳定性、安全性及用户体验的质量。本文档为开发和QA团队提供具体的测试目标、方法、工具和流程。

---

## 2. 测试目标与质量标准

-   **功能正确性**: 确保所有功能都符合 `USER_STORIES.md` 中定义的验收标准。
-   **代码质量**: 
    -   **单元测试代码覆盖率**: 核心业务逻辑模块（如计划生成、数据分析）的覆盖率 **不得低于80%**。
    -   **Linting**: 所有提交的代码 **必须** 通过 ESLint 检查，无任何错误。
-   **性能**: 
    -   **核心页面加载性能**: 主要页面（如首页、社区）的 Largest Contentful Paint (LCP) 时间应 **小于2.5秒**。
    -   **API响应时间**: 95%的API请求响应时间应 **小于500ms**。
-   **安全性**: **不得** 存在OWASP Top 10中的高危漏洞。

---

## 3. 测试金字塔与类型

我们将严格遵循测试金字塔模型，合理分配不同层级的测试资源。

```mermaid
graph TD
    A[端到端测试 (E2E) - 10%];
    B[集成测试 - 20%];
    C[单元测试 - 70%];
    A --> B; 
    B --> C;
```

### **3.1. 单元测试 (Unit Tests)**
-   **目的**: 验证最小代码单元（函数、组件）的正确性。
-   **工具**: **Vitest** + **React Testing Library**。
-   **范围**: 
    -   所有的工具函数 (`/lib` 目录)。
    -   核心业务逻辑服务（如 `PlanService`, `AnalyticsService`）。
    -   独立的React组件的渲染和基本交互。
-   **执行**: 在每次代码提交时于CI环境中自动运行。

### **3.2. 集成测试 (Integration Tests)**
-   **目的**: 验证模块之间的交互是否正确，特别是前端与Supabase后端的集成。
-   **工具**: **Vitest** + **Mock Service Worker (MSW)**。
-   **范围**: 
    -   测试前端服务层能否正确调用Supabase API并处理响应/错误。
    -   测试涉及多个组件协作的复杂UI流程。
    -   使用MSW拦截API请求，返回预设的mock数据，以隔离后端依赖，确保测试稳定性。
-   **执行**: 在每次代码提交时于CI环境中自动运行。

### **3.3. 端到端测试 (E2E Tests)**
-   **目的**: 模拟真实用户，验证贯穿整个应用的核心业务流程。
-   **工具**: **Playwright**。
-   **范围 (关键测试用例)**:
    1.  **用户注册与登录流程**: `用户访问首页 -> 点击注册 -> 填写表单 -> 提交 -> 验证邮箱 -> 自动登录 -> 登出 -> 重新登录`。
    2.  **核心打卡流程**: `登录 -> 访问首页 -> 点击“打卡” -> 选择状态 -> 提交 -> 在日历视图中验证记录`。
    3.  **社区发帖流程**: `登录 -> 进入社区 -> 点击“发帖” -> 填写内容 -> 提交 -> 在帖子列表中验证新帖子`。
    4.  **计划创建流程**: `新用户注册 -> 完成初始设置 -> 系统自动生成计划 -> 在计划页面验证计划内容`。
-   **执行**: 在合并到 `main` 分支前，于一个独立的、连接到测试数据库的预览环境中运行。

---

## 4. 特殊类型测试

### **4.1. 用户验收测试 (UAT)**
-   **阶段**: Beta测试阶段。
-   **方法**: 邀请约50-100名真实用户使用预发布版本，并提供一个结构化的反馈表单（或通过Discord频道收集）。UAT旨在发现真实使用场景下的问题和体验缺陷。

### **4.2. 性能测试**
-   **工具**: **Lighthouse** (集成在CI中), **Vercel Analytics**。
-   **方法**: 
    -   在CI流程中集成Lighthouse检查，如果核心页面的性能得分低于90分，则构建失败。
    -   上线后，通过Vercel Analytics持续监控真实用户的性能数据。

### **4.3. 安全测试**
-   **工具**: **GitHub Dependabot**, **Snyk** (或类似的依赖项扫描工具), **OWASP ZAP** (手动)。
-   **方法**: 
    -   CI流程中自动扫描已知漏洞的依赖项。
    -   在重大版本发布前，由安全负责人或外部顾问进行手动的渗透测试，检查是否存在OWASP Top 10漏洞。

---

## 5. 测试环境与数据

-   **开发环境**: 连接到Supabase的开发项目。
-   **CI/测试环境**: 连接到一个独立的Supabase测试项目。每次测试运行前，会通过脚本重置数据库，并填充预设的测试数据（如测试用户、帖子等），以保证测试的可重复性。
-   **预览环境**: 每个PR会部署到一个独立的URL，连接到测试数据库，用于E2E测试和手动验证。
-   **生产环境**: 生产环境，严格隔离。
