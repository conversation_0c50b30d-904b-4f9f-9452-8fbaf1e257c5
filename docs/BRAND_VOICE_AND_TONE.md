# 品牌声音与语调指南

**文档状态**: 生效中
**所有者**: 产品负责人/运营负责人

---

## 1. 目的

本文档定义了 Upbase 品牌在所有沟通渠道中应使用的声音（Voice）和语调（Tone）。我们的目标是建立一个一致、可识别、并能与用户建立深度信任关系的品牌形象。所有面向用户的文案撰写都必须遵循此指南。

---

## 2. 品牌声音 (Brand Voice)

品牌声音是我们的核心个性，它在任何时候都保持不变。Upbase 的声音是：

**1. 科学严谨的 (Scientific & Authoritative)**
   - 我们基于证据和科学研究，而不是空洞的口号或伪科学。我们的语言是精确、清晰、有逻辑的。

**2. 富有同理心的 (Empathetic & Supportive)**
   - 我们深刻理解用户的挣扎与不易。我们从不指责、不评判。我们的语言是温暖的、充满理解和支持的。

**3. 积极赋能的 (Positive & Empowering)**
   - 我们相信用户有能力改变。我们专注于进步、成长和掌控生活。我们的语言是鼓舞人心的、赋予力量的。

**4. 值得信赖的 (Trustworthy & Transparent)**
   - 我们对用户诚实、透明。我们清晰地解释我们的功能和数据使用方式。我们的语言是直接、坦诚、负责任的。

---

## 3. 品牌语调 (Brand Tone)

品牌语调是我们声音在不同情境下的具体应用。语调会根据场景和用户情绪进行调整。

### **场景一: 庆祝与鼓励 (Celebration & Encouragement)**
-   **情境**: 用户达成里程碑（如连续打卡7天）、完成挑战。
-   **语调**: **热情洋溢、充满赞赏、令人振奋。**

| 👍 Do (这样做)                                               | 👎 Don't (不要这样做)                                      |
| ------------------------------------------------------------ | ---------------------------------------------------------- |
| “太棒了！您已连续坚持7天。这是毅力的证明，为您感到骄傲！”     | “成功打卡7天。” (过于平淡)                                  |
| “一个新的里程碑已解锁！每一次进步，都在塑造一个更强大的你。” | “恭喜完成任务，获得积分+30。” (过于机械化)                  |

### **场景二: 日常引导与UI文本 (Guidance & UI Text)**
-   **情境**: 按钮、标签、表单提示、功能介绍。
-   **语调**: **清晰简洁、直接友好、乐于助人。**

| 👍 Do (这样做)                                               | 👎 Don't (不要这样做)                                      |
| ------------------------------------------------------------ | ---------------------------------------------------------- |
| “记录今天的心情，有助于了解你的情绪模式。”                   | “心情” (过于简单，缺少引导)                                |
| “准备好了吗？开始你的第一个7天挑战吧！”                     | “点击开始” (缺乏情感)                                      |
| “保存更改”                                                   | “执行操作” (使用技术术语)                                  |

### **场景三: 提醒与通知 (Reminders & Notifications)**
-   **情境**: 推送通知提醒用户打卡或进行冥想。
-   **语调**: **温和尊重、恰到好处、不打扰。**

| 👍 Do (这样做)                                               | 👎 Don't (不要这样做)                                      |
| ------------------------------------------------------------ | ---------------------------------------------------------- |
| “晚上好，今天过得如何？别忘了记录下今天的进步哦。”           | “警告！你今天还没打卡！” (命令式，引发焦虑)                 |
| “10分钟的正念练习，能帮你清空思绪，更好地入眠。”            | “快来做任务！” (过于急切)                                   |

### **场景四: 面对挫折 (Handling Setbacks)**
-   **情境**: 用户记录了一次破戒。
-   **语调**: **极度包容、绝不指责、聚焦未来。** 这是最能体现我们同理心和支持性声音的场景。

| 👍 Do (这样做)                                               | 👎 Don't (不要这样做)                                      |
| ------------------------------------------------------------ | ---------------------------------------------------------- |
| “没关系，这只是旅程中的一小步。重要的是，你没有放弃。让我们一起看看发生了什么，明天重新开始。” | “失败了。你的连续记录已中断。” (冷酷，打击用户)             |
| “记录这次的触发因素，是避免下次重蹈覆辙的关键一步。每一次反思，都让你离成功更近。” | “错误：打卡失败。” (技术化，缺乏人情味)                     |

### **场景五: 错误与技术问题 (Errors & Technical Issues)**
-   **情境**: 应用出现错误，或某个功能无法使用。
-   **语调**: **坦诚透明、表达歉意、提供方案。**

| 👍 Do (这样做)                                               | 👎 Don't (不要这样做)                                      |
| ------------------------------------------------------------ | ---------------------------------------------------------- |
| “抱歉，服务器开小差了。我们正在紧急修复，请稍后再试。”       | “错误代码：500” (用户无法理解)                               |
| “无法连接到网络。请检查你的网络设置后重试。”               | “操作失败。” (信息模糊，没有提供帮助)                       |
