
# Web应用开发策略与初步规划

本文档旨在明确项目在正式编码前的技术选型、核心数据库结构以及首个开发里程碑，作为后续开发工作的核心指导文件。

---

## 1. 技术选型与工具集 (Tech Stack & Tooling)

为确保开发效率、代码质量和最终产品的统一性，我们确定以下技术与工具组合：

*   **核心框架**: **Next.js 13+ (App Router)** - 提供现代化的Web开发体验，兼具服务端渲染和静态站点生成的优势。
*   **UI 组件与样式**: **Tailwind CSS + shadcn/ui**
    *   **理由**: 这不是一个传统的组件库，而是提供了一系列高质量、可定制的组件源码。我们拥有100%的控制权，可以按需修改，并与 Tailwind CSS 完美集成。这是当前 Next.js 生态中最受欢迎的组合，能让我们快速构建出美观、一致且高度可维护的界面。
*   **代码规范**: **ESLint + Prettier**
    *   **理由**: 自动格式化代码、检查潜在错误。确保所有代码风格统一，提升代码质量和可读性，是专业项目开发的标准配置。

---

## 2. 核心数据库表结构设计 (Supabase)

这是功能开发前的关键一步。以下是在 Supabase 中规划的核心数据表，并包含了至关重要的 **行级安全策略 (Row Level Security, RLS)** 的初步构想。

### a. `profiles` (用户信息表)

用于存储与 `auth.users` 表关联的公开用户信息。

*   **字段**:
    *   `id`: `uuid` (主键, 关联 `auth.users.id`)
    *   `username`: `text` (唯一的用户名)
    *   `avatar_url`: `text` (头像图片的URL)
    *   `created_at`: `timestamp with time zone`
*   **RLS 策略**:
    *   **SELECT**: 允许所有用户读取。
    *   **INSERT**: 用户只能为自己创建信息 (`auth.uid() = id`)。
    *   **UPDATE**: 用户只能更新自己的信息 (`auth.uid() = id`)。

### b. `journals` (日记表)

用于存储用户的私密日记和打卡记录。

*   **字段**:
    *   `id`: `uuid` (主键)
    *   `user_id`: `uuid` (外键, 关联 `profiles.id`)
    *   `content`: `text` (日记内容)
    *   `mood`: `text` (心情, 可设计为枚举: `happy`, `calm`, `sad` 等)
    *   `created_at`: `timestamp with time zone`
*   **RLS 策略 (核心隐私)**:
    *   **ALL**: 用户只能对自己 (`auth.uid() = user_id`) 的记录进行增删改查。

### c. `posts` (社区帖子表)

社区功能的核心，存储用户发布的帖子。

*   **字段**:
    *   `id`: `uuid` (主键)
    *   `user_id`: `uuid` (外键, 关联 `profiles.id`)
    *   `title`: `text` (帖子标题)
    *   `content`: `text` (帖子正文)
    *   `created_at`: `timestamp with time zone`
*   **RLS 策略**:
    *   **SELECT**: 允许所有用户读取。
    *   **INSERT**: 仅允许已认证用户创建 (`auth.role() = 'authenticated'`)。
    *   **UPDATE/DELETE**: 用户只能修改或删除自己的帖子 (`auth.uid() = user_id`)。

### d. `comments` (帖子评论表)

存储用户对帖子的回复。

*   **字段**:
    *   `id`: `uuid` (主键)
    *   `user_id`: `uuid` (外键, 关联 `profiles.id`)
    *   `post_id`: `uuid` (外键, 关联 `posts.id`)
    *   `content`: `text` (评论内容)
    *   `created_at`: `timestamp with time zone`
*   **RLS 策略**: 与 `posts` 表类似。
    *   **SELECT**: 允许所有用户读取。
    *   **INSERT**: 仅允许已认证用户创建。
    *   **UPDATE/DELETE**: 用户只能修改或删除自己的评论 (`auth.uid() = user_id`)。

---

## 3. 首个开发里程碑：用户认证流程

我们将从构建完整的用户认证流程开始，因为它是大部分核心功能的基础。

*   **开发步骤**:
    1.  **环境配置**: 在 Supabase 项目后台，配置并启用“邮箱+密码”的认证方式。
    2.  **数据库初始化**: 创建上述 `profiles` 表，并设置一个数据库函数（Trigger），在新用户注册时自动向 `profiles` 表插入一条记录。
    3.  **安全策略实施**: 为 `profiles` 表配置好上面定义的 RLS 策略。
    4.  **UI 开发**: 使用 Next.js 和 `shadcn/ui` 组件，在 `app/(auth)/` 目录下创建登录 (`login`) 和注册 (`register`) 页面的表单。
    5.  **API 对接**: 编写客户端逻辑，调用 Supabase 的 `signInWithPassword` 和 `signUp` 方法。
    6.  **路由保护**: 实现 `middleware.ts` 文件，检查用户会话状态，保护 `app/(main)/` 目录下的所有页面，未登录则重定向至 `/login`。
    7.  **功能验证**: 创建一个简单的个人资料页 (`/profile`) 和一个登出按钮，以测试完整的“注册 -> 登录 -> 访问受保护页面 -> 登出”流程。
