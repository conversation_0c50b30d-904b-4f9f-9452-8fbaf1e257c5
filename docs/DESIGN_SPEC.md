# 设计规格说明书 (完整详细版)

本文档提供了应用的全面设计规格，包括系统架构、所有核心模块的详细设计、接口定义、数据模型、安全性和性能优化策略，作为开发的权威技术蓝图。

---

## 1. 概述与技术栈

本应用是一款以戒色为主题的综合性健康管理工具，采用现代化的技术栈和科学化的方法，为用户提供个性化的戒色支持服务。应用以隐私保护为核心，结合AI驱动的个性化推荐和社区支持，帮助用户建立健康的生活习惯。

-   **前端**: Next.js 14 + TypeScript + TailwindCSS
-   **后端**: Supabase (PostgreSQL + Auth + Real-time)
-   **状态管理**: Zustand
-   **UI组件**: Radix UI + Shadcn/ui
-   **数据可视化**: Chart.js / Recharts
-   **PWA支持**: Service Worker + Workbox
-   **加密库**: Web Crypto API + crypto-js
-   **内容过滤**: 自研AI模型 + 第三方API

---

## 2. 架构设计

### 2.1. 系统架构

```mermaid
graph TB
    A[用户界面层] --> B[业务逻辑层]
    B --> C[数据访问层]
    C --> D[Supabase后端]
    
    A --> A1[React组件]
    A --> A2[状态管理]
    A --> A3[路由管理]
    
    B --> B1[用户管理]
    B --> B2[计划管理]
    B --> B3[社区服务]
    B --> B4[内容推荐]
    
    C --> C1[API客户端]
    C --> C2[本地存储]
    C --> C3[缓存管理]
    
    D --> D1[PostgreSQL]
    D --> D2[认证服务]
    D --> D3[实时通信]
    D --> D4[文件存储]
```

### 2.2. 数据流架构

```mermaid
sequenceDiagram
    participant U as 用户
    participant UI as 前端界面
    participant BL as 业务逻辑
    participant API as API层
    participant DB as 数据库
    
    U->>UI: 用户操作
    UI->>BL: 触发业务逻辑
    BL->>API: 调用API
    API->>DB: 数据操作
    DB-->>API: 返回结果
    API-->>BL: 处理响应
    BL-->>UI: 更新状态
    UI-->>U: 界面更新
```

---

## 3. 核心模块与接口设计 (完整版)

### 3.1. 用户管理模块 (User Management)
-   **职责**: 处理用户注册、登录、身份验证和个人资料管理。
-   **接口定义**:
    ```typescript
    interface UserService {
      register(email: string, password: string): Promise<User>;
      login(credentials: LoginCredentials): Promise<AuthResult>;
      updateProfile(userId: string, profile: UserProfile): Promise<void>;
      deleteAccount(userId: string): Promise<void>;
    }
    ```

### 3.2. 戒色计划管理模块 (Recovery Plan Management)
-   **职责**: 生成和管理个性化戒色计划。
-   **接口定义**:
    ```typescript
    interface PlanService {
      generatePlan(userProfile: UserProfile): Promise<RecoveryPlan>;
      updatePlan(planId: string, feedback: UserFeedback): Promise<RecoveryPlan>;
      getPlanProgress(planId: string): Promise<PlanProgress>;
    }
    ```

### 3.3. 打卡追踪模块 (Check-in Tracking)
-   **职责**: 记录用户每日状态和进度追踪。
-   **接口定义**:
    ```typescript
    interface CheckInService {
      recordCheckIn(userId: string, checkIn: CheckInData): Promise<CheckInRecord>;
      getCheckInHistory(userId: string, dateRange: DateRange): Promise<CheckInRecord[]>;
      getStreakInfo(userId: string): Promise<StreakInfo>;
    }
    ```

### 3.4. 社区互动模块 (Community Interaction)
-   **职责**: 提供匿名化的社区支持和互动功能。
-   **接口定义**:
    ```typescript
    interface CommunityService {
      createPost(userId: string, post: PostData): Promise<Post>;
      getTimeline(userId: string, filters: TimelineFilters): Promise<Post[]>;
      addComment(postId: string, comment: CommentData): Promise<Comment>;
      reportContent(contentId: string, reason: ReportReason): Promise<void>;
    }
    ```

### 3.5. 防诱惑工具模块 (Temptation Prevention)
-   **职责**: 提供内容屏蔽和紧急干预功能。
-   **接口定义**:
    ```typescript
    interface PreventionService {
      addToBlocklist(userId: string, items: BlocklistItem[]): Promise<void>;
      checkContent(content: string): Promise<ContentAnalysis>;
      triggerEmergency(userId: string, type: EmergencyType): Promise<InterventionResponse>;
      enableFocusMode(userId: string, duration: number): Promise<void>;
    }
    ```

### 3.6. 数据统计与分析模块 (Data Analytics)
-   **职责**: 提供详细的数据统计和趋势分析。
-   **接口定义**:
    ```typescript
    interface AnalyticsService {
      getStreakStatistics(userId: string): Promise<StreakStatistics>;
      generateWeeklyReport(userId: string): Promise<WeeklyReport>;
      analyzeTrends(userId: string, period: TimePeriod): Promise<TrendAnalysis>;
    }
    ```

### 3.7. 教育内容管理模块 (Educational Content)
-   **职责**: 管理和推荐教育内容和资源。
-   **接口定义**:
    ```typescript
    interface EducationService {
      getContentLibrary(category: ContentCategory): Promise<EducationalContent[]>;
      trackLearningProgress(userId: string, contentId: string): Promise<void>;
      requestExpertConsultation(userId: string, query: string): Promise<ConsultationRequest>;
    }
    ```

### 3.8. 游戏化激励系统 (Gamification System)
-   **职责**: 提供游戏化元素和激励机制。
-   **接口定义**:
    ```typescript
    interface GamificationService {
      unlockAchievement(userId: string, achievementId: string): Promise<Achievement>;
      updateUserLevel(userId: string, experience: number): Promise<UserLevel>;
      createChallenge(challengeData: ChallengeData): Promise<Challenge>;
    }
    ```

### 3.9. 医疗服务集成模块 (Medical Service Integration)
-   **职责**: 集成专业医疗服务和健康监测。
-   **接口定义**:
    ```typescript
    interface MedicalService {
      requestConsultation(userId: string, symptoms: string[]): Promise<ConsultationSession>;
      scheduleAppointment(userId: string, appointmentData: AppointmentData): Promise<Appointment>;
      conductHealthAssessment(userId: string): Promise<HealthAssessment>;
    }
    ```

### 3.10. 智能内容屏蔽系统 (Intelligent Content Blocking)
-   **职责**: 智能识别和屏蔽不良内容。
-   **接口定义**:
    ```typescript
    interface ContentBlockingService {
      analyzeContent(content: string, context: ContentContext): Promise<ContentAnalysis>;
      addCustomBlockRule(userId: string, rule: BlockingRule): Promise<void>;
      getBlockingStatistics(userId: string): Promise<BlockingStatistics>;
    }
    ```

### 3.11. 紧急干预与危机处理模块 (Crisis Intervention)
-   **职责**: 处理紧急情况和危机干预。
-   **接口定义**:
    ```typescript
    interface CrisisInterventionService {
      detectCrisisSignals(userId: string, behaviorData: BehaviorData): Promise<CrisisAssessment>;
      triggerEmergencyProtocol(userId: string, crisisType: CrisisType): Promise<InterventionResponse>;
      connectToHotline(userId: string): Promise<HotlineConnection>;
    }
    ```

### 3.12. 个性化推荐系统 (Personalized Recommendation)
-   **职责**: 基于用户行为提供个性化内容推荐。
-   **接口定义**:
    ```typescript
    interface RecommendationService {
      getPersonalizedContent(userId: string): Promise<RecommendedContent[]>;
      updateUserPreferences(userId: string, preferences: UserPreferences): Promise<void>;
      getNextStepRecommendations(userId: string): Promise<NextStepRecommendation[]>;
    }
    ```

---

## 4. 数据模型设计 (完整ERD)

```mermaid
erDiagram
    User ||--o{ RecoveryPlan : has
    User ||--o{ CheckInRecord : creates
    User ||--o{ Post : authors
    User ||--o{ UserAchievement : earns
    User ||--o{ BlocklistItem : configures
    User ||--o{ HealthMetric : tracks
    User ||--o{ ConsultationSession : requests
    User ||--o{ EmergencyContact : defines
    User ||--o{ LearningProgress : has
    User ||--o{ UserLevel : has
    User ||--o{ Challenge : participates

    RecoveryPlan ||--|{ DailyTask : contains
    RecoveryPlan ||--|{ Milestone : defines

    Post ||--o{ Comment : has

    EducationalContent ||--o{ LearningProgress : tracks

    Achievement ||--o{ UserAchievement : granted_to

    Doctor ||--o{ ConsultationSession : handles

    User {
        uuid id PK
        string email UK
        string encrypted_password
        jsonb profile
        jsonb privacy_settings
        timestamp created_at
        timestamp updated_at
    }

    RecoveryPlan {
        uuid id PK
        uuid user_id FK
        jsonb plan_data
        string status
        timestamp created_at
    }

    CheckInRecord {
        uuid id PK
        uuid user_id FK
        date check_in_date
        string status
        integer mood_level
        text notes
        timestamp created_at
    }

    Post {
        uuid id PK
        uuid user_id FK
        string anonymous_id
        text content
        string type
        boolean is_moderated
        timestamp created_at
    }

    Comment {
        uuid id PK
        uuid post_id FK
        uuid user_id FK
        text content
        timestamp created_at
    }

    BlocklistItem {
        uuid id PK
        uuid user_id FK
        string type
        string value
        string category
        timestamp created_at
    }

    HealthMetric {
        uuid id PK
        uuid user_id FK
        string metric_type
        jsonb metric_data
        date recorded_date
    }

    Achievement {
        uuid id PK
        string title
        text description
        string badge_url
        string rarity
    }

    UserAchievement {
        uuid id PK
        uuid user_id FK
        uuid achievement_id FK
        timestamp unlocked_at
    }

    EducationalContent {
        uuid id PK
        string title
        text content
        string category
        string difficulty_level
    }

    ConsultationSession {
        uuid id PK
        uuid user_id FK
        uuid doctor_id FK
        string status
        timestamp scheduled_at
    }

    EmergencyContact {
        uuid id PK
        uuid user_id FK
        string name
        string phone
        string relationship
    }
```

---

## 5. 错误处理

### 5.1. 错误分类和处理策略
-   **网络错误**: 采用自动重试和友好提示相结合的方式。
-   **数据验证错误**: 在客户端和服务端进行双重验证，并给出明确的错误字段和信息。
-   **隐私保护错误**: 建立严格的错误处理机制，防止任何潜在的数据泄露。

```typescript
// 网络错误处理示例
class NetworkErrorHandler {
  static async handleApiError(error: ApiError): Promise<void> {
    switch (error.type) {
      case 'NETWORK_TIMEOUT':
        await this.retryWithBackoff(error.request);
        break;
      case 'SERVER_ERROR':
        NotificationService.showError('服务暂时不可用，请稍后重试');
        break;
      case 'UNAUTHORIZED':
        await AuthService.refreshToken();
        break;
    }
  }
}
```

---

## 6. 测试策略

### 6.1. 测试金字塔
-   **单元测试 (70%)**: 使用 Jest/Vitest 对独立的组件、服务和工具函数进行测试。
-   **集成测试 (20%)**: 测试前后端接口的连通性、数据库的交互以及与Supabase服务的集成。
-   **端到端测试 (10%)**: 使用 Cypress 或 Playwright 模拟真实用户场景，测试关键的用户流程。

### 6.2. 测试环境
-   **数据库**: 使用独立的Supabase测试项目，确保测试数据与生产数据隔离。
-   **认证**: 使用mock认证提供者，预置测试用户。
-   **外部服务**: Mock所有第三方外部服务，确保测试的稳定性和确定性。

---

## 7. 安全考虑

### 7.1. 数据保护策略
-   **隐私优先设计**: 最小化数据收集，敏感数据（如笔记、健康记录）在客户端使用Web Crypto API进行端到端加密后再存储。
-   **匿名化处理**: 社区互动强制采用匿名身份，隐藏用户的真实ID。

### 7.2. 访问控制
-   **行级安全 (RLS)**: 在Supabase数据库中配置严格的RLS策略，确保用户只能访问自己的数据。
-   **API速率限制**: 对关键API端点（如登录、注册）进行速率限制，防止暴力破解和DDoS攻击。
-   **内容审核**: 结合自动化AI审核和人工审核，过滤社区中的不当内容。

### 7.3. 合规性
-   遵循GDPR等数据保护法规，明确告知用户数据用途，并提供数据导出和删除的权利。

---

## 8. 性能优化

### 8.1. 前端性能
-   **代码分割**: 按路由和组件进行代码分割，使用Next.js的动态导入 (`dynamic`) 实现懒加载。
-   **状态管理**: 使用Zustand进行高效、轻量级的状态管理，避免不必要的重渲染。
-   **缓存**: 对静态资源使用长期缓存策略；使用React Query (TanStack Query) 对API响应进行缓存、同步和后台更新。
-   **PWA**: 利用Service Worker缓存核心应用外壳和数据，实现快速加载和离线支持。

### 8.2. 后端性能
-   **数据库优化**: 为高频查询的列（如`user_id`, `created_at`, `post_id`）创建索引。对于复杂的聚合查询，使用物化视图或数据库函数。
-   **实时功能优化**: 智能管理WebSocket连接，仅在需要实时更新的页面建立连接。使用Supabase的广播功能高效分发消息。
-   **CDN加速**: 利用Vercel和Supabase内置的CDN，加速全球用户的静态资源和API访问速度。

---

## 9. 部署和运维

(详细内容参见 `DEPLOYMENT.md`)

---

## 10. 可访问性 (Accessibility)

-   **键盘导航**: 确保所有交互元素都可以通过键盘访问和操作。
-   **屏幕阅读器**: 使用语义化HTML和ARIA标签，为屏幕阅读器用户提供良好的体验。
-   **色彩与对比度**: 遵循WCAG 2.1 AA标准，确保文本和背景有足够的对比度。

