# 贡献与开发规范

欢迎为本项目做出贡献！为了确保代码质量、保持项目一致性并促进高效协作，请所有开发者遵循以下规范。

---

## 1. Git 工作流 (Git Workflow)

本项目采用基于 **GitHub Flow** 的简化工作流。

1.  **`main` 分支**: 此分支是项目的主分支，始终保持可部署（production-ready）状态。**严禁直接向 `main` 分支推送代码**。

2.  **功能分支 (Feature Branches)**:
    -   所有新功能、修复或任何代码改动都必须在从 `main` 分支创建的新分支上进行。
    -   分支命名应清晰地描述其目的，格式为 `类型/简短描述`。例如：
        -   `feat/user-authentication`
        -   `fix/login-button-bug`
        -   `refactor/database-schema`
        -   `docs/update-readme`

3.  **拉取请求 (Pull Request - PR)**:
    -   当功能开发完成后，向 `main` 分支发起一个 Pull Request。
    -   PR 的标题和描述应清晰地说明本次改动的内容、目的以及（如果适用）相关的Issue编号。
    -   在PR中，应确保所有自动化检查（CI/CD流程中的Lint、Test等）都已通过。

4.  **代码审查 (Code Review)**:
    -   每个PR必须由至少一名其他团队成员进行审查和批准后，才能合并到 `main` 分支。
    -   审查者应关注代码质量、逻辑正确性、是否遵循规范以及潜在的性能问题。

5.  **合并与清理**: PR被批准并合并后，应删除对应的功能分支。

```mermaid
graph TD
    A[main 分支] --> B(创建 feat/new-feature 分支);
    B --> C{进行代码开发和提交};
    C --> D(推送至远程仓库);
    D --> E{创建 Pull Request (PR)};
    E --> F(自动化CI检查);
    E --> G(代码审查与讨论);
    F & G --> H{批准PR};
    H --> I(合并到 main 分支);
    I --> J(部署到生产环境);
    I --> K(删除 feat/new-feature 分支);
```

---

## 2. Commit 消息规范

所有 Git commit 消息都必须遵循 **Conventional Commits** 规范。这有助于自动化生成CHANGELOG，并使提交历史清晰易读。

格式为：`<类型>(<范围>): <主题>`

-   **类型 (Type)**: 必须是以下之一：
    -   `feat`: 新功能
    -   `fix`: Bug修复
    -   `docs`: 仅文档更改
    -   `style`: 不影响代码含义的更改（空格、格式、缺少分号等）
    -   `refactor`: 代码重构，既不修复错误也不添加功能
    -   `perf`: 提升性能的代码更改
    -   `test`: 添加或修改测试
    -   `build`: 影响构建系统或外部依赖项的更改（例如：npm, webpack）
    -   `ci`: 对CI配置文件和脚本的更改
    -   `chore`: 其他不修改`src`或`test`文件的更改

-   **范围 (Scope)**: (可选) 指明本次提交影响的范围（如模块名：`auth`, `community`, `db`）。

-   **主题 (Subject)**: 简明扼要地描述本次提交的内容。

**示例:**

```
feat(auth): implement user password reset via email
fix(community): prevent XSS in post content
docs(readme): add setup instructions for new developers
```

---

## 3. 代码风格指南

-   **自动化格式化**: 项目使用 **Prettier** 进行代码自动格式化。请在提交代码前确保已运行格式化命令，或在IDE中配置保存时自动格式化。
-   **代码质量检查 (Linting)**: 项目使用 **ESLint** 进行代码质量检查。所有代码必须通过Lint检查，无任何错误。
-   **命名规范**:
    -   **组件**: PascalCase (e.g., `UserProfileCard.tsx`)
    -   **变量/函数**: camelCase (e.g., `getUserProfile`)
    -   **常量**: UPPER_SNAKE_CASE (e.g., `MAX_LOGIN_ATTEMPTS`)
    -   **类型/接口**: PascalCase (e.g., `interface UserProfile`)
-   **注释**: 只在必要时添加注释，优先编写自解释的代码。注释应解释“为什么”而不是“做什么”。

---

## 4. 本地环境搭建

1.  **克隆仓库**: `git clone <repository-url>`
2.  **安装依赖**: `npm install`
3.  **配置环境变量**: 复制 `.env.example` 文件为 `.env.local`，并填入从Supabase项目获取的URL和anon key。
4.  **运行开发服务器**: `npm run dev`
5.  **打开浏览器**: 访问 `http://localhost:3000`

---

## 5. 代码审查清单 (Code Review Checklist)

审查者在批准PR前，应检查以下几点：

-   [ ] **目的明确**: PR是否解决了它声称要解决的问题？
-   [ ] **遵循规范**: 代码是否遵循了本文件中定义的所有规范（Git、Commit、代码风格）？
-   [ ] **逻辑正确**: 代码逻辑是否正确、健壮，是否处理了边界情况？
-   [ ] **可读性与可维护性**: 代码是否清晰易懂？是否易于未来修改？
-   [ ] **无调试代码**: 是否移除了所有临时的调试代码（如 `console.log`）？
-   [ ] **测试覆盖**: 对于新功能或Bug修复，是否添加了相应的测试？
-   [ ] **文档更新**: 如果有必要，相关的文档（如 `README.md`, `API_SPEC.md`）是否已更新？
