# API 规范文档

本文档定义了应用所有API的详细规范，包括端点、请求/响应格式、状态码和数据模型。

---

## 1. 通用约定

-   **基地址**: 所有API都通过Supabase提供，RESTful端点的基地址为 `/rest/v1/`。
-   **认证**: 所有需要认证的API请求必须在 `Authorization` 头中包含 `Bearer {JWT}`。
-   **数据格式**: 所有请求和响应体均为 `application/json` 格式。
-   **错误处理**: API错误将返回标准的HTTP状态码，响应体中包含详细的错误信息。
    ```json
    {
      "code": "23505",
      "details": "Key (email)=(<EMAIL>) already exists.",
      "hint": null,
      "message": "duplicate key value violates unique constraint \"users_email_key\""
    }
    ```

---

## 2. 核心API端点详解

### 2.1. 用户管理 (User Management)

#### **注册: `POST /auth/v1/signup`**
-   **描述**: 创建一个新用户。
-   **请求体**:
    ```json
    {
      "email": "<EMAIL>",
      "password": "strong-password-123"
    }
    ```
-   **成功响应 (200 OK)**:
    ```json
    {
      "id": "uuid-goes-here",
      "aud": "authenticated",
      "role": "authenticated",
      "email": "<EMAIL>",
      "created_at": "...",
      "..."
    }
    ```

#### **登录: `POST /auth/v1/token?grant_type=password`**
-   **描述**: 用户登录并获取JWT。
-   **请求体**:
    ```json
    {
      "email": "<EMAIL>",
      "password": "strong-password-123"
    }
    ```
-   **成功响应 (200 OK)**:
    ```json
    {
      "access_token": "ey...",
      "token_type": "bearer",
      "expires_in": 3600,
      "refresh_token": "ey...",
      "user": { ... }
    }
    ```

#### **更新用户资料: `PATCH /rest/v1/users?id=eq.{user_id}`**
-   **描述**: 更新当前登录用户的个人资料。
-   **请求体**:
    ```json
    {
      "profile": {
        "nickname": "New Nickname",
        "recoveryStartDate": "2025-01-01T00:00:00Z"
      }
    }
    ```
-   **成功响应 (204 No Content)**

---

### 2.2. 戒色计划 (Recovery Plan)

#### **获取计划: `GET /rest/v1/plans?select=*`**
-   **描述**: 获取当前用户的戒色计划。
-   **成功响应 (200 OK)**:
    ```json
    [
      {
        "id": "plan-uuid",
        "user_id": "user-uuid",
        "plan_data": {
          "dailyTasks": [ ... ],
          "milestones": [ ... ]
        },
        "status": "active",
        "created_at": "..."
      }
    ]
    ```

---

### 2.3. 打卡记录 (Check-in)

#### **创建打卡: `POST /rest/v1/check-ins`**
-   **描述**: 为当天创建一个新的打卡记录。
-   **请求体**:
    ```json
    {
      "user_id": "user-uuid",
      "check_in_date": "2025-07-19",
      "status": "success",
      "mood_level": 5,
      "notes": "今天感觉很好！"
    }
    ```
-   **成功响应 (201 Created)**

---

*此为部分核心API的详细规范。完整的API文档将覆盖所有12个模块的CRUD操作，并为每个端点提供详细的参数说明、请求/响应示例和可能的错误码.*
