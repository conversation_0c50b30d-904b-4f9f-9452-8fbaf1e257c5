# 内部安全策略 (Security Policy)

**文档状态**: 草案
**所有者**: CTO/技术负责人
**审查周期**: 每6个月或在发生重大安全事件后

---

## 1. 目的与范围

本文件为 **内部文档**，旨在为 Upbase 应用的开发、部署和运维团队提供一套统一、强制性的安全标准和操作规程。其核心目标是保护用户数据的机密性、完整性和可用性，并防范潜在的安全威胁。

本策略覆盖了从代码开发到生产环境运维的全过程。

---

## 2. 数据分级

所有数据根据其敏感性分为以下四个级别，并采取相应的保护措施：

| 级别         | 描述                                       | 示例                                                              | 保护要求                                                     |
| ------------ | ------------------------------------------ | ----------------------------------------------------------------- | ------------------------------------------------------------ |
| **4: 绝密**  | 用户生成的、极度敏感的个人隐私数据。       | 用户日记、情绪记录、破戒原因、与医生的咨询记录。                    | **必须** 在客户端进行端到端加密（E2EE）；严格的行级安全（RLS）；最小化日志记录。 |
| **3: 机密**  | 用户的个人身份信息（PII）。                 | 电子邮箱、密码哈希、用户个人资料（年龄等）、IP地址。                | 传输中加密（TLS）；静态加密；严格的RLS；访问需授权和记录。     |
| **2: 内部**  | 仅供内部团队使用的运营数据。               | 用户行为分析数据（已匿名化）、系统性能指标、部署脚本。              | 访问需内部网络或VPN；基于角色的访问控制（RBAC）。              |
| **1: 公开**  | 可供公众访问的非敏感信息。                 | 公开的教育文章、应用商店描述、官网内容。                            | 无特殊保护要求，但需保证其完整性不被篡改。                   |

---

## 3. 威胁模型与主要风险

我们识别以下主要威胁，并以此为基础制定防御策略：

-   **数据泄露**: 未经授权的数据库访问，导致用户数据（尤其是级别4和3）泄露。
-   **账户劫持**: 攻击者通过暴力破解、凭证填充或XSS等手段获取用户账户控制权。
-   **社区内容滥用**: 在社区发布垃圾信息、恶意链接或进行网络钓鱼。
-   **依赖项漏洞**: 项目使用的第三方库（npm包）中存在已知漏洞，被攻击者利用。
-   **内部人员威胁**: 内部员工的误操作或恶意行为导致数据泄露或系统破坏。

---

## 4. 安全开发生命周期 (SDLC) 规范

1.  **代码审查**: 所有向 `main` 分支合并的 Pull Request **必须** 经过安全审查。审查清单需包含：
    -   是否正确实现了行级安全（RLS）查询？
    -   所有用户输入是否都经过了严格的清理和验证，以防止XSS和SQL注入？
    -   敏感数据是否在存储前进行了加密？
    -   是否有硬编码的密钥或密码？

2.  **依赖项管理**: 
    -   **必须** 启用 GitHub Dependabot，自动扫描并创建PR以更新存在漏洞的依赖项。
    -   定期（每两周）运行 `npm audit --production` 并修复发现的高危漏洞。

3.  **密钥管理**: 
    -   **严禁** 在代码库中硬编码任何API密钥、密码或其他凭证。
    -   所有密钥 **必须** 通过 Vercel 和 Supabase 的环境变量进行管理。
    -   `.env.local` 文件 **必须** 包含在 `.gitignore` 中。

---

## 5. 基础设施与访问控制

-   **最小权限原则**: 所有团队成员对 Vercel、Supabase、GitHub 等平台的访问权限都应遵循最小权限原则。
-   **多因素认证 (MFA)**: 所有团队成员 **必须** 为其 GitHub、Supabase 和其他关键内部系统账户启用MFA。
-   **数据库访问**: 
    -   **严禁** 开启公共的数据库直接访问端口。
    -   所有数据库操作应通过 Supabase API 进行，并受RLS策略保护。
    -   任何临时的直接数据库访问（仅限DBA或技术负责人）都必须通过安全的堡垒机，并记录操作日志。
-   **行级安全 (RLS)**: **必须** 为所有存储用户数据的表启用RLS。默认策略应为拒绝所有访问，然后为特定查询和操作创建策略。例如：`CREATE POLICY "Users can view their own profile." ON profiles FOR SELECT USING (auth.uid() = id);`

---

## 6. 事件响应计划

如发生或疑似发生安全事件（如数据泄露），应立即启动以下流程：

1.  **检测与报告**: 任何发现潜在安全事件的团队成员，应立即向技术负责人和指定的“事件响应指挥官”报告。
2.  **评估与分级**: 事件响应指挥官负责评估事件的严重性，并组建核心响应团队。
3.  **遏制 (Containment)**: 
    -   立即采取措施隔离受影响的系统，防止事态扩大。例如：轮换泄露的API密钥、禁用受影响的账户、增加防火墙规则。
    -   创建事件专属的Slack频道用于协调沟通。
4.  **根除 (Eradication)**: 
    -   调查事件的根本原因（如代码漏洞、配置错误）。
    -   部署补丁，修复漏洞。
5.  **恢复 (Recovery)**: 
    -   确认漏洞已修复后，逐步将系统恢复到正常运行状态。
    -   持续监控，确保攻击者未再次进入。
6.  **事后复盘 (Post-Mortem)**: 
    -   事件解决后一周内，进行“无指责”的事后复盘会议。
    -   分析事件全过程，总结经验教训，并更新本文档和相关流程以防止未来再次发生。
7.  **沟通与披露**: 根据事件的性质和法律要求（如GDPR），由管理层决定是否以及如何向用户或监管机构进行披露。
