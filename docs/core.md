技术栈

Supabase + Postgresql + Next.js + TailwindCSS + shadcn/ui + lucide-react（Icon）

尽可能的利用好Supabase的API功能和权限系统，减少后端开发工作量


# 提示词

我要做一款戒色为主题的应用，先做web页面（响应式，需要适配移动端），用web版验证需求，后期再做App (Android 和 IOS), App可以规划功能，但是当前不需要实现



Supabase + Postgresql + Next.js + TailwindCSS

尽可能的利用好Supabase的API功能和权限系统，减少后端开发工作量

我的初步调研文档在 docs 目录。帮我



完成Spec约定



Plan first, then build. Create requirements and design before coding starts.

