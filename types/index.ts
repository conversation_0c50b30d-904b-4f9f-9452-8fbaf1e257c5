// 用户相关类型
export interface User {
  id: string
  email: string
  username: string
  avatar_url?: string
  created_at: string
  profile?: UserProfile
}

export interface UserProfile {
  age?: number
  goal?: 'quit_porn' | 'reduce_frequency' | 'self_improvement'
  history?: 'beginner' | 'tried_failed' | 'experienced'
  triggers?: string[]
  positive_energy_score?: number
}

// 戒色计划相关类型
export interface RecoveryPlan {
  id: string
  user_id: string
  plan_data: PlanData
  status: 'active' | 'completed' | 'paused'
  created_at: string
  updated_at: string
}

export interface PlanData {
  duration_days: number
  daily_tasks: DailyTask[]
  milestones: Milestone[]
  strategies: Strategy[]
}

export interface DailyTask {
  id: string
  title: string
  description: string
  category: 'mindfulness' | 'physical' | 'learning' | 'hobby'
  difficulty: 1 | 2 | 3 | 4 | 5
  estimated_minutes: number
  completed: boolean
  task_date: string
}

export interface Milestone {
  id: string
  title: string
  description: string
  target_days: number
  achieved: boolean
  achieved_at?: string
}

export interface Strategy {
  id: string
  title: string
  description: string
  category: 'prevention' | 'intervention' | 'recovery'
  triggers: string[]
}

// 打卡记录相关类型
export interface CheckInRecord {
  id: string
  user_id: string
  check_in_date: string
  status: 'success' | 'relapse'
  mood_level: 1 | 2 | 3 | 4 | 5
  notes?: string
  challenges?: string[]
  created_at: string
}

// 社区相关类型
export interface Post {
  id: string
  user_id: string
  anonymous_id: string
  title: string
  content: string
  type: 'share' | 'question' | 'support'
  tags?: string[]
  cover_image_url?: string
  likes_count: number
  comments_count: number
  is_moderated: boolean
  created_at: string
  updated_at: string
}

export interface Comment {
  id: string
  post_id: string
  user_id: string
  anonymous_id: string
  content: string
  likes_count: number
  created_at: string
}

// 统计数据类型
export interface UserStats {
  current_streak: number
  longest_streak: number
  total_days: number
  success_rate: number
  positive_energy_score: number
  mood_trend: MoodData[]
  weekly_progress: WeeklyProgress[]
}

export interface MoodData {
  date: string
  mood: number
}

export interface WeeklyProgress {
  week: string
  success_days: number
  total_days: number
  success_rate: number
}

// API 响应类型
export interface ApiResponse<T> {
  data: T
  error?: string
  message?: string
}

// 表单类型
export interface LoginForm {
  email: string
  password: string
}

export interface RegisterForm {
  email: string
  password: string
  confirmPassword: string
  username: string
}

export interface OnboardingForm {
  age: number
  goal: UserProfile['goal']
  history: UserProfile['history']
  triggers: string[]
}
