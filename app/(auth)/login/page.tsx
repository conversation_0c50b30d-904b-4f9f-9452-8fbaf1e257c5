'use client'

import { useState } from 'react'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card'
import { mockSupabase } from '@/lib/supabase/client'
import { isValidEmail } from '@/lib/utils'

export default function LoginPage() {
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const router = useRouter()

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setError('')
    setLoading(true)

    // 基本验证
    if (!email || !password) {
      setError('请填写所有字段')
      setLoading(false)
      return
    }

    if (!isValidEmail(email)) {
      setError('请输入有效的邮箱地址')
      setLoading(false)
      return
    }

    try {
      // 使用模拟的 Supabase 客户端
      const { data, error: authError } = await mockSupabase.auth.signInWithPassword({
        email,
        password,
      })

      if (authError) {
        setError(authError.message)
        return
      }

      if (data.user) {
        // 保存模拟会话到 localStorage
        localStorage.setItem('mock-session', JSON.stringify(data.session))
        localStorage.setItem('mock-user', JSON.stringify(data.user))
        
        // 跳转到主应用
        router.push('/dashboard')
      }
    } catch (err) {
      setError('登录失败，请稍后重试')
    } finally {
      setLoading(false)
    }
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>登录</CardTitle>
        <CardDescription>
          欢迎回来！请登录您的账户
        </CardDescription>
      </CardHeader>
      <form onSubmit={handleSubmit}>
        <CardContent className="space-y-4">
          {error && (
            <div className="p-3 text-sm text-red-600 bg-red-50 border border-red-200 rounded-md">
              {error}
            </div>
          )}
          
          <div className="space-y-2">
            <label htmlFor="email" className="text-sm font-medium">
              邮箱
            </label>
            <Input
              id="email"
              type="email"
              placeholder="请输入邮箱"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              disabled={loading}
            />
          </div>
          
          <div className="space-y-2">
            <label htmlFor="password" className="text-sm font-medium">
              密码
            </label>
            <Input
              id="password"
              type="password"
              placeholder="请输入密码"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              disabled={loading}
            />
          </div>
        </CardContent>
        
        <CardFooter className="flex flex-col space-y-4">
          <Button 
            type="submit" 
            className="w-full" 
            loading={loading}
            variant="gradient"
          >
            登录
          </Button>
          
          <div className="text-center text-sm text-gray-600">
            还没有账户？{' '}
            <Link href="/register" className="text-blue-600 hover:underline">
              立即注册
            </Link>
          </div>
          
          <div className="text-center">
            <Link href="/forgot-password" className="text-sm text-gray-500 hover:underline">
              忘记密码？
            </Link>
          </div>
        </CardFooter>
      </form>
    </Card>
  )
}
