'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { 
  BookOpen,
  Headphones,
  MessageCircle,
  Search,
  Play,
  Pause,
  Volume2,
  Clock,
  User,
  Star,
  Filter,
  Heart,
  Brain,
  Lightbulb
} from 'lucide-react'
import { mockApiResponse } from '@/lib/mock-data'
import { formatRelativeDate } from '@/lib/utils'
import Link from 'next/link'

interface Article {
  id: string
  title: string
  content: string
  category: 'psychology' | 'mindfulness' | 'health' | 'motivation'
  author: string
  readTime: number
  likes: number
  createdAt: string
}

interface AudioResource {
  id: string
  title: string
  description: string
  duration: number
  category: 'meditation' | 'relaxation' | 'breathing' | 'sleep'
  audioUrl: string
  isPlaying: boolean
}

interface QAItem {
  id: string
  question: string
  answer: string
  expert: string
  expertTitle: string
  likes: number
  createdAt: string
}

export default function LearnPage() {
  const [articles, setArticles] = useState<Article[]>([])
  const [audioResources, setAudioResources] = useState<AudioResource[]>([])
  const [qaItems, setQAItems] = useState<QAItem[]>([])
  const [loading, setLoading] = useState(true)
  const [activeTab, setActiveTab] = useState<'articles' | 'audio' | 'qa'>('articles')
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedCategory, setSelectedCategory] = useState<string>('all')
  const [currentlyPlaying, setCurrentlyPlaying] = useState<string | null>(null)

  useEffect(() => {
    loadLearnData()
  }, [])

  const loadLearnData = async () => {
    try {
      const mockArticles: Article[] = [
        {
          id: '1',
          title: '多巴胺戒断的科学原理',
          content: '多巴胺是大脑中的一种神经递质，它在奖励系统中起着关键作用...',
          category: 'psychology',
          author: '张心理学博士',
          readTime: 8,
          likes: 156,
          createdAt: '2024-01-15T10:00:00Z'
        },
        {
          id: '2',
          title: '正念冥想的实践指南',
          content: '正念冥想是一种古老而有效的心理训练方法，可以帮助我们...',
          category: 'mindfulness',
          author: '李冥想导师',
          readTime: 12,
          likes: 203,
          createdAt: '2024-01-14T15:30:00Z'
        },
        {
          id: '3',
          title: '如何建立健康的生活习惯',
          content: '健康的生活习惯是戒色成功的重要基础，包括规律作息、适量运动...',
          category: 'health',
          author: '王健康专家',
          readTime: 6,
          likes: 89,
          createdAt: '2024-01-13T09:15:00Z'
        },
        {
          id: '4',
          title: '保持动力的心理技巧',
          content: '在戒色过程中保持动力是一个挑战，这里分享一些实用的心理技巧...',
          category: 'motivation',
          author: '陈励志导师',
          readTime: 10,
          likes: 134,
          createdAt: '2024-01-12T14:20:00Z'
        }
      ]

      const mockAudio: AudioResource[] = [
        {
          id: '1',
          title: '10分钟正念冥想',
          description: '适合初学者的基础正念冥想练习',
          duration: 600,
          category: 'meditation',
          audioUrl: '/audio/meditation-10min.mp3',
          isPlaying: false
        },
        {
          id: '2',
          title: '深度放松引导',
          description: '帮助缓解压力和焦虑的放松练习',
          duration: 900,
          category: 'relaxation',
          audioUrl: '/audio/relaxation-15min.mp3',
          isPlaying: false
        },
        {
          id: '3',
          title: '4-7-8呼吸法',
          description: '科学有效的呼吸调节技巧',
          duration: 300,
          category: 'breathing',
          audioUrl: '/audio/breathing-5min.mp3',
          isPlaying: false
        },
        {
          id: '4',
          title: '睡前冥想音乐',
          description: '帮助改善睡眠质量的舒缓音乐',
          duration: 1800,
          category: 'sleep',
          audioUrl: '/audio/sleep-30min.mp3',
          isPlaying: false
        }
      ]

      const mockQA: QAItem[] = [
        {
          id: '1',
          question: '戒色过程中出现焦虑情绪怎么办？',
          answer: '焦虑是戒色过程中的正常反应。建议采用深呼吸、正念冥想等方法来缓解。同时，保持规律的作息和适量运动也很重要。如果焦虑严重，建议寻求专业心理咨询师的帮助。',
          expert: '李心理咨询师',
          expertTitle: '国家二级心理咨询师',
          likes: 45,
          createdAt: '2024-01-15T16:00:00Z'
        },
        {
          id: '2',
          question: '如何应对戒色过程中的挫折感？',
          answer: '挫折感是成长过程中的必经之路。重要的是要学会接纳自己的不完美，将挫折视为学习的机会。建议制定小目标，逐步实现，增强自信心。同时，寻求社区支持也很有帮助。',
          expert: '王行为治疗师',
          expertTitle: '认知行为治疗专家',
          likes: 67,
          createdAt: '2024-01-14T11:30:00Z'
        },
        {
          id: '3',
          question: '戒色对身体健康有什么好处？',
          answer: '戒色可以带来多方面的健康益处：改善睡眠质量、提高精力水平、增强免疫力、改善皮肤状态等。更重要的是，它有助于提升自控力和专注力，对心理健康也有积极影响。',
          expert: '张医学博士',
          expertTitle: '泌尿外科主任医师',
          likes: 89,
          createdAt: '2024-01-13T14:45:00Z'
        }
      ]

      const [articlesResponse, audioResponse, qaResponse] = await Promise.all([
        mockApiResponse(mockArticles),
        mockApiResponse(mockAudio),
        mockApiResponse(mockQA)
      ])

      setArticles(articlesResponse.data)
      setAudioResources(audioResponse.data)
      setQAItems(qaResponse.data)
    } catch (error) {
      console.error('加载学习数据失败:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleLikeArticle = async (articleId: string) => {
    try {
      await mockApiResponse({ success: true })
      setArticles(prev => 
        prev.map(article => 
          article.id === articleId 
            ? { ...article, likes: article.likes + 1 }
            : article
        )
      )
    } catch (error) {
      console.error('点赞失败:', error)
    }
  }

  const handlePlayAudio = async (audioId: string) => {
    try {
      await mockApiResponse({ success: true })
      
      if (currentlyPlaying === audioId) {
        setCurrentlyPlaying(null)
        setAudioResources(prev => 
          prev.map(audio => ({ ...audio, isPlaying: false }))
        )
      } else {
        setCurrentlyPlaying(audioId)
        setAudioResources(prev => 
          prev.map(audio => ({
            ...audio,
            isPlaying: audio.id === audioId
          }))
        )
      }
    } catch (error) {
      console.error('播放音频失败:', error)
    }
  }

  const handleLikeQA = async (qaId: string) => {
    try {
      await mockApiResponse({ success: true })
      setQAItems(prev => 
        prev.map(qa => 
          qa.id === qaId 
            ? { ...qa, likes: qa.likes + 1 }
            : qa
        )
      )
    } catch (error) {
      console.error('点赞失败:', error)
    }
  }

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'psychology': return <Brain className="h-4 w-4 text-purple-500" />
      case 'mindfulness': return <Heart className="h-4 w-4 text-green-500" />
      case 'health': return <Lightbulb className="h-4 w-4 text-blue-500" />
      case 'motivation': return <Star className="h-4 w-4 text-yellow-500" />
      case 'meditation': return <Brain className="h-4 w-4 text-purple-500" />
      case 'relaxation': return <Heart className="h-4 w-4 text-green-500" />
      case 'breathing': return <Volume2 className="h-4 w-4 text-blue-500" />
      case 'sleep': return <Clock className="h-4 w-4 text-indigo-500" />
      default: return <BookOpen className="h-4 w-4 text-gray-500" />
    }
  }

  const getCategoryLabel = (category: string) => {
    switch (category) {
      case 'psychology': return '心理学'
      case 'mindfulness': return '正念'
      case 'health': return '健康'
      case 'motivation': return '励志'
      case 'meditation': return '冥想'
      case 'relaxation': return '放松'
      case 'breathing': return '呼吸'
      case 'sleep': return '睡眠'
      default: return '其他'
    }
  }

  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  const filteredContent = () => {
    let content: any[] = []
    
    switch (activeTab) {
      case 'articles':
        content = articles
        break
      case 'audio':
        content = audioResources
        break
      case 'qa':
        content = qaItems
        break
    }

    return content.filter(item => {
      const matchesSearch = 
        (item.title?.toLowerCase().includes(searchQuery.toLowerCase())) ||
        (item.question?.toLowerCase().includes(searchQuery.toLowerCase())) ||
        (item.content?.toLowerCase().includes(searchQuery.toLowerCase())) ||
        (item.description?.toLowerCase().includes(searchQuery.toLowerCase()))
      
      const matchesCategory = selectedCategory === 'all' || item.category === selectedCategory
      
      return matchesSearch && matchesCategory
    })
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-6"></div>
          <div className="space-y-4">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="h-32 bg-gray-200 rounded-lg"></div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  const handleTabChange = (tab: 'articles' | 'audio' | 'qa') => {
    setActiveTab(tab)
    setCurrentPage(1)
    setSelectedCategory('all')
    setSearchQuery('')
  }

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">学习中心</h1>
        <p className="text-gray-600">通过科学的知识和方法，提升自我认知和管理能力</p>
      </div>

      {/* 标签页导航 */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg">
          <Button
            variant={activeTab === 'articles' ? 'default' : 'ghost'}
            size="sm"
            onClick={() => setActiveTab('articles')}
          >
            <BookOpen className="mr-2 h-4 w-4" />
            文章
          </Button>
          <Button
            variant={activeTab === 'audio' ? 'default' : 'ghost'}
            size="sm"
            onClick={() => setActiveTab('audio')}
          >
            <Headphones className="mr-2 h-4 w-4" />
            音频
          </Button>
          <Button
            variant={activeTab === 'qa' ? 'default' : 'ghost'}
            size="sm"
            onClick={() => setActiveTab('qa')}
          >
            <MessageCircle className="mr-2 h-4 w-4" />
            问答
          </Button>
        </div>

        {/* 搜索和筛选 */}
        <div className="flex gap-2">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder="搜索内容..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10 w-64"
            />
          </div>
          <select
            value={selectedCategory}
            onChange={(e) => setSelectedCategory(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md text-sm"
          >
            <option value="all">全部分类</option>
            {activeTab === 'articles' && (
              <>
                <option value="psychology">心理学</option>
                <option value="mindfulness">正念</option>
                <option value="health">健康</option>
                <option value="motivation">励志</option>
              </>
            )}
            {activeTab === 'audio' && (
              <>
                <option value="meditation">冥想</option>
                <option value="relaxation">放松</option>
                <option value="breathing">呼吸</option>
                <option value="sleep">睡眠</option>
              </>
            )}
          </select>
        </div>
      </div>

      {/* 内容展示 */}
      <div className="space-y-4">
        {activeTab === 'articles' && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {filteredContent().map((article: Article) => (
              <Card key={article.id} className="hover:shadow-md transition-shadow">
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-2">
                        {getCategoryIcon(article.category)}
                        <span className="text-xs text-gray-500">
                          {getCategoryLabel(article.category)}
                        </span>
                        <span className="text-xs text-gray-400">•</span>
                        <span className="text-xs text-gray-500">
                          {article.readTime} 分钟阅读
                        </span>
                      </div>
                      <CardTitle className="text-lg">{article.title}</CardTitle>
                      <p className="text-sm text-gray-600 mt-1">
                        by {article.author}
                      </p>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <>
                    <p className="text-gray-700 mb-4 line-clamp-3">
                      {article.content}
                    </p>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-4">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleLikeArticle(article.id)}
                          className="text-gray-600 hover:text-red-500"
                        >
                          <Heart className="mr-1 h-4 w-4" />
                          {article.likes}
                        </Button>
                        <span className="text-xs text-gray-500">
                          {formatRelativeDate(article.createdAt)}
                        </span>
                      </div>
                      <Link href={`/article/${article.id}`}>
                        <Button variant="outline" size="sm">
                          阅读全文
                        </Button>
                      </Link>
                    </div>
                  </>
                </CardContent>
              </Card>
            ))}
          </div>
        )}

        {activeTab === 'audio' && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredContent().map((audio: AudioResource) => (
              <Card key={audio.id} className="hover:shadow-md transition-shadow">
                <CardHeader>
                  <div className="flex items-center gap-2 mb-2">
                    {getCategoryIcon(audio.category)}
                    <span className="text-xs text-gray-500">
                      {getCategoryLabel(audio.category)}
                    </span>
                    <span className="text-xs text-gray-400">•</span>
                    <span className="text-xs text-gray-500">
                      {formatDuration(audio.duration)}
                    </span>
                  </div>
                  <CardTitle className="text-lg">{audio.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-700 mb-4">
                    {audio.description}
                  </p>
                  <div className="flex items-center justify-center">
                    <Button
                      variant={audio.isPlaying ? "destructive" : "gradient"}
                      size="lg"
                      onClick={() => handlePlayAudio(audio.id)}
                      className="w-full"
                    >
                      {audio.isPlaying ? (
                        <>
                          <Pause className="mr-2 h-5 w-5" />
                          暂停播放
                        </>
                      ) : (
                        <>
                          <Play className="mr-2 h-5 w-5" />
                          开始播放
                        </>
                      )}
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}

        {activeTab === 'qa' && (
          <div className="space-y-4">
            {filteredContent().map((qa: QAItem) => (
              <Card key={qa.id} className="hover:shadow-md transition-shadow">
                <CardHeader>
                  <CardTitle className="text-lg text-blue-900">
                    Q: {qa.question}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <>
                    <div className="bg-blue-50 p-4 rounded-lg mb-4">
                      <p className="text-gray-700 mb-3">
                        <span className="font-medium text-green-700">A: </span>
                        {qa.answer}
                      </p>
                      <div className="flex items-center gap-2 text-sm text-gray-600">
                        <User className="h-4 w-4" />
                        <span className="font-medium">{qa.expert}</span>
                        <span className="text-gray-400">•</span>
                        <span>{qa.expertTitle}</span>
                      </div>
                    </div>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-4">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleLikeQA(qa.id)}
                          className="text-gray-600 hover:text-red-500"
                        >
                          <Heart className="mr-1 h-4 w-4" />
                          {qa.likes}
                        </Button>
                        <span className="text-xs text-gray-500">
                          {formatRelativeDate(qa.createdAt)}
                        </span>
                      </div>
                      <Link href={`/article/${qa.id}`}>
                        <Button variant="outline" size="sm">
                          查看详情
                        </Button>
                      </Link>
                    </div>
                  </>
                </CardContent>
              </Card>
            ))}
          </div>
        )}

        {filteredContent().length === 0 && (
          <Card>
            <CardContent className="text-center py-12">
              {activeTab === 'articles' && <BookOpen className="mx-auto h-12 w-12 text-gray-400 mb-4" />}
              {activeTab === 'audio' && <Headphones className="mx-auto h-12 w-12 text-gray-400 mb-4" />}
              {activeTab === 'qa' && <MessageCircle className="mx-auto h-12 w-12 text-gray-400 mb-4" />}
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                暂无内容
              </h3>
              <p className="text-gray-600">
                {searchQuery ? '没有找到匹配的内容' : '内容正在准备中，敬请期待'}
              </p>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  )
}
