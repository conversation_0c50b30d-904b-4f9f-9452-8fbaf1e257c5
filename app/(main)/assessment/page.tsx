'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Skeleton } from '@/components/ui/skeleton'
import { 
  Brain,
  Heart,
  Target,
  TrendingUp,
  Star,
  Award,
  CheckCircle,
  ArrowRight,
  BarChart3,
  Users,
  Calendar,
  Zap,
  Shield,
  Lightbulb
} from 'lucide-react'
import { mockApiResponse } from '@/lib/mock-data'

interface AssessmentQuestion {
  id: string
  category: 'mental' | 'physical' | 'social' | 'spiritual'
  question: string
  options: { value: number; label: string }[]
}

interface AssessmentResult {
  category: string
  score: number
  maxScore: number
  level: 'beginner' | 'intermediate' | 'advanced' | 'expert'
  improvement: number
  suggestions: string[]
}

export default function AssessmentPage() {
  const [currentStep, setCurrentStep] = useState<'intro' | 'assessment' | 'results' | 'comparison'>('intro')
  const [currentQuestion, setCurrentQuestion] = useState(0)
  const [answers, setAnswers] = useState<Record<string, number>>({})
  const [results, setResults] = useState<AssessmentResult[]>([])
  const [previousResults, setPreviousResults] = useState<AssessmentResult[]>([])
  const [isVip, setIsVip] = useState(false)
  const [loading, setLoading] = useState(false)

  const assessmentQuestions: AssessmentQuestion[] = [
    {
      id: 'mental_1',
      category: 'mental',
      question: '你对自己的情绪控制能力如何评价？',
      options: [
        { value: 1, label: '很差，经常情绪失控' },
        { value: 2, label: '一般，偶尔失控' },
        { value: 3, label: '良好，大部分时候能控制' },
        { value: 4, label: '很好，几乎总能保持冷静' }
      ]
    },
    {
      id: 'mental_2',
      category: 'mental',
      question: '面对诱惑时，你的抵抗能力如何？',
      options: [
        { value: 1, label: '很弱，经常屈服' },
        { value: 2, label: '一般，有时会屈服' },
        { value: 3, label: '较强，大多数时候能抵抗' },
        { value: 4, label: '很强，几乎不会屈服' }
      ]
    },
    {
      id: 'physical_1',
      category: 'physical',
      question: '你的整体身体状态如何？',
      options: [
        { value: 1, label: '很差，经常感到疲惫' },
        { value: 2, label: '一般，有时感到疲惫' },
        { value: 3, label: '良好，精力充沛' },
        { value: 4, label: '很好，充满活力' }
      ]
    },
    {
      id: 'physical_2',
      category: 'physical',
      question: '你的睡眠质量如何？',
      options: [
        { value: 1, label: '很差，经常失眠' },
        { value: 2, label: '一般，偶尔失眠' },
        { value: 3, label: '良好，大部分时候睡得好' },
        { value: 4, label: '很好，睡眠质量很高' }
      ]
    },
    {
      id: 'social_1',
      category: 'social',
      question: '你与他人的关系质量如何？',
      options: [
        { value: 1, label: '很差，关系紧张' },
        { value: 2, label: '一般，关系平淡' },
        { value: 3, label: '良好，关系和谐' },
        { value: 4, label: '很好，关系深厚' }
      ]
    },
    {
      id: 'social_2',
      category: 'social',
      question: '你参与社交活动的频率如何？',
      options: [
        { value: 1, label: '很少，几乎不参与' },
        { value: 2, label: '偶尔，有时参与' },
        { value: 3, label: '经常，积极参与' },
        { value: 4, label: '很多，是社交达人' }
      ]
    },
    {
      id: 'spiritual_1',
      category: 'spiritual',
      question: '你对生活的意义感如何？',
      options: [
        { value: 1, label: '很低，感到迷茫' },
        { value: 2, label: '一般，有时迷茫' },
        { value: 3, label: '较高，目标明确' },
        { value: 4, label: '很高，充满使命感' }
      ]
    },
    {
      id: 'spiritual_2',
      category: 'spiritual',
      question: '你的自我成长动力如何？',
      options: [
        { value: 1, label: '很低，缺乏动力' },
        { value: 2, label: '一般，偶尔有动力' },
        { value: 3, label: '较高，持续成长' },
        { value: 4, label: '很高，充满激情' }
      ]
    }
  ]

  useEffect(() => {
    // 模拟VIP状态
    setIsVip(Math.random() > 0.5)
  }, [])

  const handleAnswer = (questionId: string, value: number) => {
    setAnswers(prev => ({ ...prev, [questionId]: value }))
  }

  const nextQuestion = () => {
    if (currentQuestion < assessmentQuestions.length - 1) {
      setCurrentQuestion(prev => prev + 1)
    } else {
      calculateResults()
    }
  }

  const calculateResults = async () => {
    setLoading(true)
    try {
      // 计算各类别得分
      const categories = ['mental', 'physical', 'social', 'spiritual']
      const categoryNames = {
        mental: '心理健康',
        physical: '身体状态',
        social: '社交关系',
        spiritual: '精神成长'
      }

      const newResults: AssessmentResult[] = categories.map(category => {
        const categoryQuestions = assessmentQuestions.filter(q => q.category === category)
        const categoryAnswers = categoryQuestions.map(q => answers[q.id] || 0)
        const score = categoryAnswers.reduce((sum, answer) => sum + answer, 0)
        const maxScore = categoryQuestions.length * 4
        const percentage = (score / maxScore) * 100

        let level: AssessmentResult['level'] = 'beginner'
        if (percentage >= 90) level = 'expert'
        else if (percentage >= 75) level = 'advanced'
        else if (percentage >= 60) level = 'intermediate'

        const suggestions = generateSuggestions(category as any, level)
        const improvement = Math.floor(Math.random() * 20) + 5 // 模拟改善幅度

        return {
          category: categoryNames[category as keyof typeof categoryNames],
          score,
          maxScore,
          level,
          improvement,
          suggestions
        }
      })

      // 模拟之前的结果
      const mockPreviousResults: AssessmentResult[] = newResults.map(result => ({
        ...result,
        score: Math.max(1, result.score - Math.floor(Math.random() * 3) - 1),
        improvement: 0
      }))

      await mockApiResponse({ results: newResults })
      setResults(newResults)
      setPreviousResults(mockPreviousResults)
      setCurrentStep('results')
    } catch (error) {
      console.error('计算评估结果失败:', error)
    } finally {
      setLoading(false)
    }
  }

  const generateSuggestions = (category: 'mental' | 'physical' | 'social' | 'spiritual', level: string): string[] => {
    const suggestions = {
      mental: {
        beginner: ['开始每日冥想练习', '学习情绪管理技巧', '寻求专业心理支持'],
        intermediate: ['深化冥想练习', '学习认知行为疗法', '建立情绪日记'],
        advanced: ['探索高级冥想技巧', '帮助他人管理情绪', '研究心理学知识'],
        expert: ['成为情绪管理导师', '开发个人心理系统', '分享经验帮助他人']
      },
      physical: {
        beginner: ['建立规律作息', '开始简单运动', '改善饮食习惯'],
        intermediate: ['增加运动强度', '优化营养搭配', '定期体检'],
        advanced: ['制定专业训练计划', '学习运动科学', '保持长期健康'],
        expert: ['成为健身教练', '研究运动医学', '指导他人健康生活']
      },
      social: {
        beginner: ['主动与人交流', '参加社交活动', '学习沟通技巧'],
        intermediate: ['扩大社交圈子', '深化友谊关系', '学习领导技能'],
        advanced: ['组织社交活动', '成为团队领导', '帮助他人社交'],
        expert: ['成为社交专家', '指导人际关系', '建立影响力网络']
      },
      spiritual: {
        beginner: ['思考人生意义', '设定人生目标', '寻找精神支柱'],
        intermediate: ['深化价值观念', '实践个人使命', '学习哲学思想'],
        advanced: ['建立人生哲学', '指导他人成长', '追求更高境界'],
        expert: ['成为精神导师', '传播智慧思想', '影响更多生命']
      }
    }
    return suggestions[category][level as keyof typeof suggestions[typeof category]] || []
  }

  const getLevelColor = (level: string) => {
    switch (level) {
      case 'expert': return 'text-purple-600 bg-purple-100'
      case 'advanced': return 'text-blue-600 bg-blue-100'
      case 'intermediate': return 'text-green-600 bg-green-100'
      default: return 'text-yellow-600 bg-yellow-100'
    }
  }

  const getLevelName = (level: string) => {
    switch (level) {
      case 'expert': return '专家级'
      case 'advanced': return '高级'
      case 'intermediate': return '中级'
      default: return '初级'
    }
  }

  if (loading) {
    return (
      <div className="space-y-6">
        {/* 页面标题骨架 */}
        <div className="text-center space-y-2">
          <div className="flex items-center justify-center">
            <Skeleton className="w-6 h-6 mr-2" />
            <Skeleton className="h-8 w-48" />
            <Skeleton className="h-6 w-12 ml-2" />
          </div>
          <Skeleton className="h-4 w-64 mx-auto" />
        </div>

        {/* 评估进度骨架 */}
        <Card className="animate-pulse">
          <CardHeader>
            <Skeleton className="h-6 w-32" />
            <Skeleton className="h-4 w-48" />
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <Skeleton className="h-2 w-full" />
              <div className="flex justify-between">
                <Skeleton className="h-4 w-16" />
                <Skeleton className="h-4 w-12" />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 评估结果卡片骨架 */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[...Array(6)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <Skeleton className="h-6 w-24" />
                  <Skeleton className="h-5 w-12" />
                </div>
                <Skeleton className="h-4 w-full" />
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <Skeleton className="h-8 w-16" />
                  <Skeleton className="h-2 w-full" />
                  <div className="space-y-2">
                    <Skeleton className="h-3 w-full" />
                    <Skeleton className="h-3 w-3/4" />
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* 建议区域骨架 */}
        <Card className="animate-pulse">
          <CardHeader>
            <Skeleton className="h-6 w-32" />
            <Skeleton className="h-4 w-48" />
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {[...Array(3)].map((_, i) => (
                <div key={i} className="flex items-start space-x-3">
                  <Skeleton className="w-5 h-5 mt-0.5" />
                  <div className="flex-1 space-y-2">
                    <Skeleton className="h-4 w-full" />
                    <Skeleton className="h-3 w-2/3" />
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="text-center">
        <h1 className="text-2xl font-bold text-gray-900 flex items-center justify-center">
          <Brain className="mr-2 h-6 w-6 text-purple-500" />
          会员成长评估
          {isVip && <Badge className="ml-2 bg-yellow-100 text-yellow-800"><Star className="mr-1 h-3 w-3" />VIP</Badge>}
        </h1>
        <p className="text-gray-600 mt-2">科学评估您的成长状态，制定个性化改善方案</p>
      </div>

      {/* 介绍页面 */}
      {currentStep === 'intro' && (
        <div className="max-w-2xl mx-auto">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Lightbulb className="mr-2 h-5 w-5 text-yellow-500" />
                评估介绍
              </CardTitle>
              <CardDescription>
                通过科学的评估问卷，了解您在心理、身体、社交、精神四个维度的成长状态
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="p-4 bg-purple-50 rounded-lg">
                    <div className="flex items-center mb-2">
                      <Brain className="h-5 w-5 text-purple-500 mr-2" />
                      <span className="font-medium">心理健康</span>
                    </div>
                    <p className="text-sm text-gray-600">评估情绪管理和心理状态</p>
                  </div>
                  <div className="p-4 bg-green-50 rounded-lg">
                    <div className="flex items-center mb-2">
                      <Heart className="h-5 w-5 text-green-500 mr-2" />
                      <span className="font-medium">身体状态</span>
                    </div>
                    <p className="text-sm text-gray-600">评估身体健康和活力水平</p>
                  </div>
                  <div className="p-4 bg-blue-50 rounded-lg">
                    <div className="flex items-center mb-2">
                      <Users className="h-5 w-5 text-blue-500 mr-2" />
                      <span className="font-medium">社交关系</span>
                    </div>
                    <p className="text-sm text-gray-600">评估人际关系和社交能力</p>
                  </div>
                  <div className="p-4 bg-yellow-50 rounded-lg">
                    <div className="flex items-center mb-2">
                      <Star className="h-5 w-5 text-yellow-500 mr-2" />
                      <span className="font-medium">精神成长</span>
                    </div>
                    <p className="text-sm text-gray-600">评估人生意义和成长动力</p>
                  </div>
                </div>
                
                <div className="p-4 bg-gray-50 rounded-lg">
                  <h4 className="font-medium text-gray-900 mb-2">评估特色：</h4>
                  <ul className="text-sm text-gray-600 space-y-1">
                    <li>• 科学的四维度评估体系</li>
                    <li>• 个性化的改善建议</li>
                    <li>• 与历史数据的对比分析</li>
                    {isVip && <li>• VIP专享：详细分析报告和专家建议</li>}
                  </ul>
                </div>

                <Button 
                  className="w-full" 
                  onClick={() => setCurrentStep('assessment')}
                >
                  开始评估 ({assessmentQuestions.length} 个问题)
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* 评估问卷 */}
      {currentStep === 'assessment' && (
        <div className="max-w-2xl mx-auto">
          <div className="mb-6">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm text-gray-600">
                问题 {currentQuestion + 1} / {assessmentQuestions.length}
              </span>
              <span className="text-sm text-gray-600">
                {Math.round(((currentQuestion + 1) / assessmentQuestions.length) * 100)}%
              </span>
            </div>
            <Progress value={((currentQuestion + 1) / assessmentQuestions.length) * 100} />
          </div>

          <Card>
            <CardHeader>
              <CardTitle className="text-lg">
                {assessmentQuestions[currentQuestion].question}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {assessmentQuestions[currentQuestion].options.map((option, index) => (
                  <button
                    key={index}
                    onClick={() => {
                      handleAnswer(assessmentQuestions[currentQuestion].id, option.value)
                      setTimeout(nextQuestion, 300)
                    }}
                    className="w-full p-4 text-left border rounded-lg hover:bg-gray-50 hover:border-blue-300 transition-colors"
                  >
                    <div className="flex items-center">
                      <div className="w-4 h-4 border-2 border-gray-300 rounded-full mr-3"></div>
                      {option.label}
                    </div>
                  </button>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* 评估结果 */}
      {currentStep === 'results' && (
        <div className="space-y-6">
          <div className="text-center">
            <div className="inline-flex items-center justify-center w-16 h-16 bg-green-100 rounded-full mb-4">
              <CheckCircle className="h-8 w-8 text-green-500" />
            </div>
            <h2 className="text-xl font-semibold text-gray-900 mb-2">评估完成！</h2>
            <p className="text-gray-600">以下是您的详细评估结果和改善建议</p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {results.map((result, index) => (
              <Card key={index}>
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <span>{result.category}</span>
                    <Badge className={getLevelColor(result.level)}>
                      {getLevelName(result.level)}
                    </Badge>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div>
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-sm text-gray-600">得分</span>
                        <span className="font-semibold">{result.score}/{result.maxScore}</span>
                      </div>
                      <Progress value={(result.score / result.maxScore) * 100} />
                    </div>
                    
                    <div>
                      <h4 className="font-medium text-gray-900 mb-2">改善建议：</h4>
                      <ul className="text-sm text-gray-600 space-y-1">
                        {result.suggestions.map((suggestion, idx) => (
                          <li key={idx} className="flex items-start">
                            <span className="text-blue-500 mr-2">•</span>
                            {suggestion}
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          <div className="flex gap-4 justify-center">
            <Button 
              variant="outline"
              onClick={() => setCurrentStep('comparison')}
            >
              <BarChart3 className="mr-2 h-4 w-4" />
              查看对比分析
            </Button>
            <Button onClick={() => {
              setCurrentStep('intro')
              setCurrentQuestion(0)
              setAnswers({})
            }}>
              重新评估
            </Button>
          </div>
        </div>
      )}

      {/* 对比分析 */}
      {currentStep === 'comparison' && (
        <div className="space-y-6">
          <div className="text-center">
            <h2 className="text-xl font-semibold text-gray-900 mb-2">成长对比分析</h2>
            <p className="text-gray-600">与您的历史数据进行对比，了解成长轨迹</p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {results.map((result, index) => {
              const previous = previousResults[index]
              const improvement = result.score - previous.score
              const improvementPercentage = previous.score > 0 ? (improvement / previous.score) * 100 : 0

              return (
                <Card key={index}>
                  <CardHeader>
                    <CardTitle className="flex items-center justify-between">
                      <span>{result.category}</span>
                      {improvement > 0 && (
                        <Badge className="bg-green-100 text-green-800">
                          <TrendingUp className="mr-1 h-3 w-3" />
                          +{improvement}
                        </Badge>
                      )}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="grid grid-cols-2 gap-4">
                        <div className="text-center p-3 bg-gray-50 rounded-lg">
                          <div className="text-sm text-gray-600 mb-1">之前</div>
                          <div className="text-lg font-semibold">{previous.score}/{previous.maxScore}</div>
                        </div>
                        <div className="text-center p-3 bg-blue-50 rounded-lg">
                          <div className="text-sm text-gray-600 mb-1">现在</div>
                          <div className="text-lg font-semibold text-blue-600">{result.score}/{result.maxScore}</div>
                        </div>
                      </div>
                      
                      {improvement > 0 && (
                        <div className="p-3 bg-green-50 rounded-lg">
                          <div className="text-sm text-green-800">
                            🎉 恭喜！您在{result.category}方面提升了 {improvement} 分
                            {improvementPercentage > 0 && ` (${improvementPercentage.toFixed(1)}%)`}
                          </div>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              )
            })}
          </div>

          {isVip && (
            <Card className="border-yellow-200 bg-gradient-to-r from-yellow-50 to-orange-50">
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Award className="mr-2 h-5 w-5 text-yellow-600" />
                  VIP专享：专家分析报告
                  <Badge className="ml-2 bg-yellow-100 text-yellow-800">VIP</Badge>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="p-4 bg-white/80 rounded-lg">
                    <h4 className="font-medium text-gray-900 mb-2">🧠 心理专家建议：</h4>
                    <p className="text-sm text-gray-700">
                      根据您的评估结果，建议重点关注情绪管理技能的提升。您在抗压能力方面表现良好，
                      但在情绪表达和沟通方面还有提升空间。建议每周进行2-3次正念冥想练习。
                    </p>
                  </div>
                  <div className="p-4 bg-white/80 rounded-lg">
                    <h4 className="font-medium text-gray-900 mb-2">📈 成长轨迹分析：</h4>
                    <p className="text-sm text-gray-700">
                      您的整体成长趋势非常积极，特别是在精神成长和身体状态方面进步明显。
                      建议继续保持当前的成长节奏，同时加强社交关系的建设。
                    </p>
                  </div>
                  <div className="p-4 bg-white/80 rounded-lg">
                    <h4 className="font-medium text-gray-900 mb-2">🎯 下阶段目标：</h4>
                    <ul className="text-sm text-gray-700 space-y-1">
                      <li>• 建立每日冥想习惯，提升心理韧性</li>
                      <li>• 参加至少2次社交活动，扩展人际网络</li>
                      <li>• 制定3个月的身体锻炼计划</li>
                      <li>• 寻找一个有意义的志愿服务项目</li>
                    </ul>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          <div className="flex gap-4 justify-center">
            <Button 
              variant="outline"
              onClick={() => setCurrentStep('results')}
            >
              返回结果
            </Button>
            <Button onClick={() => {
              setCurrentStep('intro')
              setCurrentQuestion(0)
              setAnswers({})
            }}>
              重新评估
            </Button>
          </div>
        </div>
      )}
    </div>
  )
}
