'use client'

import { useState, useEffect, useRef } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { 
  Bot,
  Send,
  Mic,
  MicOff,
  Brain,
  Heart,
  Shield,
  TrendingUp,
  AlertTriangle,
  CheckCircle,
  Clock,
  Sparkles,
  MessageCircle,
  Lightbulb,
  Target,
  Activity
} from 'lucide-react'
import { mockApiResponse } from '@/lib/mock-data'
import { Skeleton } from '@/components/ui/skeleton'

// 生成AI助手数据
function generateAIAssistantData() {
  return {
    userProfile: {
      name: '用户',
      currentStreak: 23,
      riskLevel: 'low', // low, medium, high
      lastCheckIn: new Date().toISOString(),
      personalityType: 'analytical',
      preferredSupport: 'cognitive'
    },
    riskAssessment: {
      currentRisk: 25, // 0-100
      riskFactors: [
        { factor: '压力水平', level: 'medium', impact: 30 },
        { factor: '睡眠质量', level: 'good', impact: 10 },
        { factor: '社交活动', level: 'low', impact: 20 },
        { factor: '工作强度', level: 'high', impact: 35 }
      ],
      recommendations: [
        '建议增加户外运动时间',
        '保持规律的作息时间',
        '尝试冥想或深呼吸练习',
        '与朋友保持更多联系'
      ]
    },
    chatHistory: [
      {
        id: 'msg-1',
        type: 'ai',
        content: '你好！我是你的AI戒色助手小智。我注意到你已经坚持了23天，表现很棒！今天感觉怎么样？',
        timestamp: new Date(Date.now() - 300000).toISOString(),
        mood: 'supportive'
      },
      {
        id: 'msg-2',
        type: 'user',
        content: '今天压力有点大，工作很忙',
        timestamp: new Date(Date.now() - 240000).toISOString()
      },
      {
        id: 'msg-3',
        type: 'ai',
        content: '我理解工作压力会影响情绪。根据你的情况，我建议你现在做5分钟的深呼吸练习。要不要我引导你做一次？',
        timestamp: new Date(Date.now() - 180000).toISOString(),
        mood: 'caring',
        suggestions: ['开始呼吸练习', '查看放松音乐', '安排休息时间']
      }
    ],
    insights: [
      {
        type: 'pattern',
        title: '行为模式洞察',
        content: '你在工作日的晚上7-9点风险较高，建议在这个时间段安排运动或社交活动。',
        confidence: 85
      },
      {
        type: 'progress',
        title: '进步分析',
        content: '相比上个月，你的整体情绪稳定性提升了30%，睡眠质量也有明显改善。',
        confidence: 92
      },
      {
        type: 'recommendation',
        title: '个性化建议',
        content: '基于你的分析型人格，建议使用数据追踪和目标设定来维持动力。',
        confidence: 78
      }
    ]
  }
}

export default function AIAssistantPage() {
  const [assistantData, setAssistantData] = useState<any>(null)
  const [message, setMessage] = useState('')
  const [chatHistory, setChatHistory] = useState<any[]>([])
  const [isTyping, setIsTyping] = useState(false)
  const [isListening, setIsListening] = useState(false)
  const [loading, setLoading] = useState(false)
  const messagesEndRef = useRef<HTMLDivElement>(null)

  const loadAssistantData = async () => {
    setLoading(true)
    try {
      const response = await mockApiResponse(generateAIAssistantData())
      setAssistantData(response.data)
      setChatHistory(response.data.chatHistory)
    } catch (error) {
      console.error('加载AI助手数据失败:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    loadAssistantData()
  }, [])

  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }, [chatHistory])

  const sendMessage = async () => {
    if (!message.trim()) return

    const userMessage = {
      id: `msg-${Date.now()}`,
      type: 'user',
      content: message,
      timestamp: new Date().toISOString()
    }

    setChatHistory(prev => [...prev, userMessage])
    setMessage('')
    setIsTyping(true)

    // 模拟AI回复
    setTimeout(() => {
      const aiResponse = generateAIResponse(message)
      setChatHistory(prev => [...prev, aiResponse])
      setIsTyping(false)
    }, 1500)
  }

  const generateAIResponse = (userMessage: string) => {
    const responses = [
      {
        content: '我理解你的感受。让我们一起分析一下当前的情况，找到最适合你的应对方式。',
        mood: 'understanding',
        suggestions: ['情绪分析', '制定计划', '寻求支持']
      },
      {
        content: '根据你的描述，我建议你现在专注于当下，尝试一些放松技巧。你想试试哪种方法？',
        mood: 'supportive',
        suggestions: ['深呼吸练习', '正念冥想', '听音乐']
      },
      {
        content: '很好的问题！基于你的历史数据，我发现了一些有用的模式。让我为你详细分析一下。',
        mood: 'analytical',
        suggestions: ['查看数据', '制定策略', '设置提醒']
      }
    ]

    const randomResponse = responses[Math.floor(Math.random() * responses.length)]
    
    return {
      id: `msg-${Date.now()}`,
      type: 'ai',
      content: randomResponse.content,
      timestamp: new Date().toISOString(),
      mood: randomResponse.mood,
      suggestions: randomResponse.suggestions
    }
  }

  const getRiskColor = (level: string) => {
    switch (level) {
      case 'low': return 'text-green-600 bg-green-100'
      case 'medium': return 'text-yellow-600 bg-yellow-100'
      case 'high': return 'text-red-600 bg-red-100'
      default: return 'text-gray-600 bg-gray-100'
    }
  }

  const getMoodIcon = (mood: string) => {
    switch (mood) {
      case 'supportive': return <Heart className="h-4 w-4 text-pink-500" />
      case 'caring': return <Shield className="h-4 w-4 text-blue-500" />
      case 'analytical': return <Brain className="h-4 w-4 text-purple-500" />
      case 'understanding': return <Lightbulb className="h-4 w-4 text-yellow-500" />
      default: return <Bot className="h-4 w-4 text-gray-500" />
    }
  }

  if (!assistantData) {
    return (
      <div className="space-y-6">
        {/* 页面标题骨架 */}
        <div className="space-y-2">
          <Skeleton className="h-8 w-32" />
          <Skeleton className="h-4 w-64" />
        </div>

        {/* AI助手状态卡片骨架 */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {[...Array(3)].map((_, i) => (
            <Card key={i}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <Skeleton className="h-5 w-20" />
                <Skeleton className="h-4 w-4" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-8 w-16 mb-2" />
                <Skeleton className="h-3 w-24" />
              </CardContent>
            </Card>
          ))}
        </div>

        {/* 聊天界面骨架 */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* 聊天区域骨架 */}
          <div className="lg:col-span-2">
            <Card className="h-[600px] flex flex-col">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <Skeleton className="h-10 w-10 rounded-full" />
                    <div className="space-y-1">
                      <Skeleton className="h-5 w-24" />
                      <Skeleton className="h-3 w-16" />
                    </div>
                  </div>
                  <Skeleton className="h-8 w-20" />
                </div>
              </CardHeader>
              <CardContent className="flex-1 flex flex-col">
                {/* 聊天消息骨架 */}
                <div className="flex-1 space-y-4 mb-4">
                  {[...Array(4)].map((_, i) => (
                    <div key={i} className={`flex ${i % 2 === 0 ? 'justify-start' : 'justify-end'}`}>
                      <div className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${i % 2 === 0 ? 'bg-gray-100' : 'bg-blue-100'}`}>
                        <Skeleton className="h-4 w-32 mb-1" />
                        <Skeleton className="h-4 w-24" />
                      </div>
                    </div>
                  ))}
                </div>

                {/* 输入区域骨架 */}
                <div className="flex space-x-2">
                  <Skeleton className="flex-1 h-10" />
                  <Skeleton className="h-10 w-10" />
                  <Skeleton className="h-10 w-10" />
                </div>
              </CardContent>
            </Card>
          </div>

          {/* 侧边栏骨架 */}
          <div className="space-y-6">
            {/* 快速操作骨架 */}
            <Card>
              <CardHeader>
                <Skeleton className="h-6 w-20" />
                <Skeleton className="h-4 w-32" />
              </CardHeader>
              <CardContent className="space-y-3">
                {[...Array(4)].map((_, i) => (
                  <div key={i} className="flex items-center space-x-3 p-3 border rounded-lg">
                    <Skeleton className="h-5 w-5" />
                    <div className="flex-1 space-y-1">
                      <Skeleton className="h-4 w-20" />
                      <Skeleton className="h-3 w-16" />
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>

            {/* 使用统计骨架 */}
            <Card>
              <CardHeader>
                <Skeleton className="h-6 w-20" />
                <Skeleton className="h-4 w-32" />
              </CardHeader>
              <CardContent className="space-y-4">
                {[...Array(3)].map((_, i) => (
                  <div key={i} className="space-y-2">
                    <div className="flex justify-between">
                      <Skeleton className="h-4 w-16" />
                      <Skeleton className="h-4 w-8" />
                    </div>
                    <Skeleton className="h-2 w-full" />
                  </div>
                ))}
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 flex items-center">
            <Bot className="mr-2 h-6 w-6 text-blue-500" />
            AI智能助手
          </h1>
          <p className="text-gray-600">个性化的戒色指导和心理支持</p>
        </div>
        <Badge className="bg-blue-100 text-blue-800">
          <Sparkles className="mr-1 h-3 w-3" />
          Beta版本
        </Badge>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* 左侧 - 风险评估和洞察 */}
        <div className="space-y-6">
          {/* 风险评估 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center text-sm">
                <AlertTriangle className="mr-2 h-4 w-4" />
                风险评估
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="text-center">
                  <div className="text-3xl font-bold text-green-600 mb-1">
                    {assistantData.riskAssessment.currentRisk}%
                  </div>
                  <div className="text-sm text-gray-600">当前风险水平</div>
                  <Progress value={assistantData.riskAssessment.currentRisk} className="mt-2" />
                </div>
                
                <div className="space-y-2">
                  <h4 className="font-medium text-sm">风险因素分析</h4>
                  {assistantData.riskAssessment.riskFactors.map((factor: any, index: number) => (
                    <div key={index} className="flex items-center justify-between text-sm">
                      <span className="text-gray-600">{factor.factor}</span>
                      <Badge variant="outline" className={getRiskColor(factor.level)}>
                        {factor.impact}%
                      </Badge>
                    </div>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* AI洞察 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center text-sm">
                <Brain className="mr-2 h-4 w-4" />
                AI洞察
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {assistantData.insights.map((insight: any, index: number) => (
                  <div key={index} className="p-3 bg-gray-50 rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-medium text-sm">{insight.title}</h4>
                      <Badge variant="outline" className="text-xs">
                        {insight.confidence}%
                      </Badge>
                    </div>
                    <p className="text-xs text-gray-600">{insight.content}</p>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* 中间和右侧 - 聊天界面 */}
        <div className="lg:col-span-2">
          <Card className="h-[600px] flex flex-col">
            <CardHeader>
              <CardTitle className="flex items-center">
                <MessageCircle className="mr-2 h-5 w-5" />
                与小智对话
              </CardTitle>
              <CardDescription>
                你的专属AI助手，随时为你提供支持和指导
              </CardDescription>
            </CardHeader>
            <CardContent className="flex-1 flex flex-col">
              {/* 聊天记录 */}
              <div className="flex-1 overflow-y-auto space-y-4 mb-4">
                {chatHistory.map((msg: any) => (
                  <div key={msg.id} className={`flex ${msg.type === 'user' ? 'justify-end' : 'justify-start'}`}>
                    <div className={`max-w-[80%] ${
                      msg.type === 'user' 
                        ? 'bg-blue-500 text-white' 
                        : 'bg-gray-100 text-gray-900'
                    } rounded-lg p-3`}>
                      {msg.type === 'ai' && (
                        <div className="flex items-center mb-2">
                          {getMoodIcon(msg.mood)}
                          <span className="ml-2 text-xs font-medium">小智</span>
                        </div>
                      )}
                      <p className="text-sm">{msg.content}</p>
                      {msg.suggestions && (
                        <div className="flex flex-wrap gap-1 mt-2">
                          {msg.suggestions.map((suggestion: string, index: number) => (
                            <Button
                              key={index}
                              size="sm"
                              variant="outline"
                              className="text-xs h-6"
                              onClick={() => setMessage(suggestion)}
                            >
                              {suggestion}
                            </Button>
                          ))}
                        </div>
                      )}
                      <div className="text-xs opacity-70 mt-1">
                        {new Date(msg.timestamp).toLocaleTimeString()}
                      </div>
                    </div>
                  </div>
                ))}
                
                {isTyping && (
                  <div className="flex justify-start">
                    <div className="bg-gray-100 rounded-lg p-3">
                      <div className="flex items-center space-x-1">
                        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{animationDelay: '0.1s'}}></div>
                        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{animationDelay: '0.2s'}}></div>
                      </div>
                    </div>
                  </div>
                )}
                <div ref={messagesEndRef} />
              </div>

              {/* 输入区域 */}
              <div className="flex items-center space-x-2">
                <Input
                  value={message}
                  onChange={(e) => setMessage(e.target.value)}
                  placeholder="输入你的问题或感受..."
                  onKeyPress={(e) => e.key === 'Enter' && sendMessage()}
                  className="flex-1"
                />
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => setIsListening(!isListening)}
                  className={isListening ? 'bg-red-100 text-red-600' : ''}
                >
                  {isListening ? <MicOff className="h-4 w-4" /> : <Mic className="h-4 w-4" />}
                </Button>
                <Button onClick={sendMessage} disabled={!message.trim()}>
                  <Send className="h-4 w-4" />
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* 快速建议 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Target className="mr-2 h-5 w-5" />
            今日建议
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {assistantData.riskAssessment.recommendations.map((rec: string, index: number) => (
              <div key={index} className="p-4 border rounded-lg hover:shadow-md transition-shadow cursor-pointer">
                <div className="flex items-center mb-2">
                  <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                  <span className="text-sm font-medium">建议 {index + 1}</span>
                </div>
                <p className="text-sm text-gray-600">{rec}</p>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
