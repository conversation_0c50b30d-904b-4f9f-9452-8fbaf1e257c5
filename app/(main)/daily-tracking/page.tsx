'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import {
  Calendar,
  CheckCircle,
  Clock,
  Target,
  TrendingUp,
  Heart,
  Brain,
  Activity,
  Moon,
  Smile,
  Frown,
  Meh,
  Plus,
  Save,
  BarChart3,
  LineChart,
  Award,
  Flame,
  Star
} from 'lucide-react'
import { mockApiResponse } from '@/lib/mock-data'
import { Skeleton } from '@/components/ui/skeleton'
import { format, subDays, startOfWeek, endOfWeek } from 'date-fns'

// 生成每日追踪数据
function generateDailyTrackingData() {
  const today = new Date()
  const trackingData = {
    today: {
      date: format(today, 'yyyy-MM-dd'),
      completed: false,
      mood: 0,
      energy: 0,
      sleep_hours: 0,
      exercise_minutes: 0,
      meditation_minutes: 0,
      urge_intensity: 0,
      urge_frequency: 0,
      stress_level: 0,
      productivity: 0,
      social_interaction: 0,
      notes: '',
      goals_completed: 0,
      total_goals: 5
    },
    weekly_summary: {
      week_start: format(startOfWeek(today), 'yyyy-MM-dd'),
      week_end: format(endOfWeek(today), 'yyyy-MM-dd'),
      days_tracked: 5,
      avg_mood: 4.2,
      avg_energy: 3.8,
      avg_sleep: 7.5,
      total_exercise: 180,
      total_meditation: 75,
      streak_days: 23,
      goals_completion_rate: 78
    },
    recent_entries: [] as any[]
  }

  // 生成最近7天的数据
  for (let i = 1; i <= 7; i++) {
    const date = subDays(today, i)
    trackingData.recent_entries.push({
      date: format(date, 'yyyy-MM-dd'),
      mood: Math.floor(Math.random() * 3) + 3, // 3-5
      energy: Math.floor(Math.random() * 3) + 3,
      sleep_hours: Math.floor(Math.random() * 3) + 6, // 6-8
      exercise_minutes: Math.floor(Math.random() * 60) + 30,
      meditation_minutes: Math.floor(Math.random() * 20) + 5,
      urge_intensity: Math.floor(Math.random() * 3) + 1, // 1-3
      urge_frequency: Math.floor(Math.random() * 3) + 1,
      stress_level: Math.floor(Math.random() * 3) + 2, // 2-4
      productivity: Math.floor(Math.random() * 3) + 3,
      social_interaction: Math.floor(Math.random() * 3) + 2,
      goals_completed: Math.floor(Math.random() * 4) + 2,
      total_goals: 5,
      notes: i === 1 ? '今天感觉很好，完成了所有计划的任务' : ''
    })
  }

  return trackingData
}

export default function DailyTrackingPage() {
  const [trackingData, setTrackingData] = useState<any>(null)
  const [todayData, setTodayData] = useState<any>(null)
  const [loading, setLoading] = useState(false)
  const [saving, setSaving] = useState(false)

  const loadTrackingData = async () => {
    setLoading(true)
    try {
      const response = await mockApiResponse(generateDailyTrackingData())
      setTrackingData(response.data)
      setTodayData(response.data.today)
    } catch (error) {
      console.error('加载追踪数据失败:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    loadTrackingData()
  }, [])

  const handleSave = async () => {
    setSaving(true)
    try {
      // 模拟保存数据
      await mockApiResponse({ success: true }, 1000)
      setTodayData({ ...todayData, completed: true })
      // 这里可以添加成功提示
    } catch (error) {
      console.error('保存失败:', error)
    } finally {
      setSaving(false)
    }
  }

  const updateTodayData = (field: string, value: any) => {
    setTodayData({ ...todayData, [field]: value })
  }

  const getMoodIcon = (mood: number) => {
    if (mood >= 4) return <Smile className="h-5 w-5 text-green-500" />
    if (mood >= 3) return <Meh className="h-5 w-5 text-yellow-500" />
    return <Frown className="h-5 w-5 text-red-500" />
  }

  const getMoodColor = (mood: number) => {
    if (mood >= 4) return 'text-green-600'
    if (mood >= 3) return 'text-yellow-600'
    return 'text-red-600'
  }

  if (!trackingData || !todayData) {
    return (
      <div className="space-y-6">
        {/* 页面标题骨架 */}
        <div className="space-y-2">
          <Skeleton className="h-8 w-32" />
          <Skeleton className="h-4 w-64" />
        </div>

        {/* 今日追踪卡片骨架 */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div className="space-y-2">
                <Skeleton className="h-6 w-24" />
                <Skeleton className="h-4 w-40" />
              </div>
              <Skeleton className="h-8 w-20" />
            </div>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* 情绪追踪骨架 */}
            <div className="space-y-3">
              <Skeleton className="h-5 w-16" />
              <div className="flex justify-between items-center">
                {[...Array(5)].map((_, i) => (
                  <Skeleton key={i} className="h-12 w-12 rounded-full" />
                ))}
              </div>
            </div>

            {/* 睡眠质量骨架 */}
            <div className="space-y-3">
              <Skeleton className="h-5 w-16" />
              <div className="flex justify-between items-center">
                {[...Array(5)].map((_, i) => (
                  <Skeleton key={i} className="h-12 w-12 rounded-full" />
                ))}
              </div>
            </div>

            {/* 压力水平骨架 */}
            <div className="space-y-3">
              <Skeleton className="h-5 w-16" />
              <div className="flex justify-between items-center">
                {[...Array(5)].map((_, i) => (
                  <Skeleton key={i} className="h-12 w-12 rounded-full" />
                ))}
              </div>
            </div>

            {/* 笔记区域骨架 */}
            <div className="space-y-3">
              <Skeleton className="h-5 w-20" />
              <Skeleton className="h-24 w-full" />
            </div>

            {/* 按钮骨架 */}
            <div className="flex gap-2">
              <Skeleton className="h-10 w-20" />
              <Skeleton className="h-10 w-24" />
            </div>
          </CardContent>
        </Card>

        {/* 历史数据卡片骨架 */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <Skeleton className="h-6 w-32" />
              <Skeleton className="h-4 w-48" />
            </CardHeader>
            <CardContent>
              <Skeleton className="h-64 w-full" />
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <Skeleton className="h-6 w-32" />
              <Skeleton className="h-4 w-48" />
            </CardHeader>
            <CardContent className="space-y-3">
              {[...Array(5)].map((_, i) => (
                <div key={i} className="flex items-center justify-between p-3 border rounded">
                  <div className="space-y-1">
                    <Skeleton className="h-4 w-20" />
                    <Skeleton className="h-3 w-16" />
                  </div>
                  <Skeleton className="h-6 w-12" />
                </div>
              ))}
            </CardContent>
          </Card>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 flex items-center">
            <Calendar className="mr-2 h-6 w-6 text-blue-500" />
            每日追踪
          </h1>
          <p className="text-gray-600">记录每日状态，追踪成长进步</p>
        </div>
        <div className="flex items-center gap-2">
          <Badge variant="outline" className="text-orange-600">
            <Flame className="mr-1 h-3 w-3" />
            连续 {trackingData.weekly_summary.streak_days} 天
          </Badge>
          <Badge variant="outline" className="text-green-600">
            <CheckCircle className="mr-1 h-3 w-3" />
            本周已追踪 {trackingData.weekly_summary.days_tracked} 天
          </Badge>
        </div>
      </div>

      {/* 今日追踪 */}
      <Card className={`${todayData.completed ? 'border-green-200 bg-green-50' : 'border-blue-200 bg-blue-50'}`}>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center">
              <Clock className="mr-2 h-5 w-5" />
              今日追踪 - {format(new Date(), 'MM月dd日')}
            </div>
            {todayData.completed && (
              <Badge className="bg-green-500">
                <CheckCircle className="mr-1 h-3 w-3" />
                已完成
              </Badge>
            )}
          </CardTitle>
          <CardDescription>
            记录今天的身心状态和目标完成情况
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {/* 情绪状态 */}
            <div className="space-y-3">
              <label className="text-sm font-medium text-gray-700 flex items-center">
                <Heart className="mr-2 h-4 w-4 text-red-500" />
                情绪状态
              </label>
              <div className="flex gap-2">
                {[1, 2, 3, 4, 5].map((value) => (
                  <button
                    key={value}
                    onClick={() => updateTodayData('mood', value)}
                    className={`w-10 h-10 rounded-full border-2 flex items-center justify-center transition-colors ${
                      todayData.mood === value
                        ? 'border-blue-500 bg-blue-100'
                        : 'border-gray-300 hover:border-gray-400'
                    }`}
                  >
                    {value}
                  </button>
                ))}
              </div>
              <div className="text-xs text-gray-500">1=很差 5=很好</div>
            </div>

            {/* 精力水平 */}
            <div className="space-y-3">
              <label className="text-sm font-medium text-gray-700 flex items-center">
                <Activity className="mr-2 h-4 w-4 text-orange-500" />
                精力水平
              </label>
              <div className="flex gap-2">
                {[1, 2, 3, 4, 5].map((value) => (
                  <button
                    key={value}
                    onClick={() => updateTodayData('energy', value)}
                    className={`w-10 h-10 rounded-full border-2 flex items-center justify-center transition-colors ${
                      todayData.energy === value
                        ? 'border-orange-500 bg-orange-100'
                        : 'border-gray-300 hover:border-gray-400'
                    }`}
                  >
                    {value}
                  </button>
                ))}
              </div>
              <div className="text-xs text-gray-500">1=疲惫 5=充沛</div>
            </div>

            {/* 睡眠时长 */}
            <div className="space-y-3">
              <label className="text-sm font-medium text-gray-700 flex items-center">
                <Moon className="mr-2 h-4 w-4 text-purple-500" />
                睡眠时长
              </label>
              <Input
                type="number"
                placeholder="小时"
                value={todayData.sleep_hours || ''}
                onChange={(e) => updateTodayData('sleep_hours', parseFloat(e.target.value) || 0)}
                className="w-full"
              />
              <div className="text-xs text-gray-500">建议7-9小时</div>
            </div>

            {/* 运动时长 */}
            <div className="space-y-3">
              <label className="text-sm font-medium text-gray-700 flex items-center">
                <Target className="mr-2 h-4 w-4 text-green-500" />
                运动时长
              </label>
              <Input
                type="number"
                placeholder="分钟"
                value={todayData.exercise_minutes || ''}
                onChange={(e) => updateTodayData('exercise_minutes', parseInt(e.target.value) || 0)}
                className="w-full"
              />
              <div className="text-xs text-gray-500">建议30分钟以上</div>
            </div>

            {/* 冥想时长 */}
            <div className="space-y-3">
              <label className="text-sm font-medium text-gray-700 flex items-center">
                <Brain className="mr-2 h-4 w-4 text-indigo-500" />
                冥想时长
              </label>
              <Input
                type="number"
                placeholder="分钟"
                value={todayData.meditation_minutes || ''}
                onChange={(e) => updateTodayData('meditation_minutes', parseInt(e.target.value) || 0)}
                className="w-full"
              />
              <div className="text-xs text-gray-500">建议10分钟以上</div>
            </div>

            {/* 冲动强度 */}
            <div className="space-y-3">
              <label className="text-sm font-medium text-gray-700 flex items-center">
                <TrendingUp className="mr-2 h-4 w-4 text-red-500" />
                冲动强度
              </label>
              <div className="flex gap-2">
                {[1, 2, 3, 4, 5].map((value) => (
                  <button
                    key={value}
                    onClick={() => updateTodayData('urge_intensity', value)}
                    className={`w-10 h-10 rounded-full border-2 flex items-center justify-center transition-colors ${
                      todayData.urge_intensity === value
                        ? 'border-red-500 bg-red-100'
                        : 'border-gray-300 hover:border-gray-400'
                    }`}
                  >
                    {value}
                  </button>
                ))}
              </div>
              <div className="text-xs text-gray-500">1=很弱 5=很强</div>
            </div>
          </div>

          {/* 今日目标完成情况 */}
          <div className="mt-6 space-y-3">
            <label className="text-sm font-medium text-gray-700 flex items-center">
              <Award className="mr-2 h-4 w-4 text-yellow-500" />
              今日目标完成情况
            </label>
            <div className="flex items-center gap-4">
              <div className="flex-1">
                <Progress value={(todayData.goals_completed / todayData.total_goals) * 100} />
              </div>
              <div className="text-sm text-gray-600">
                {todayData.goals_completed} / {todayData.total_goals}
              </div>
            </div>
            <div className="flex gap-2">
              <Button
                size="sm"
                variant="outline"
                onClick={() => updateTodayData('goals_completed', Math.max(0, todayData.goals_completed - 1))}
              >
                -
              </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={() => updateTodayData('goals_completed', Math.min(todayData.total_goals, todayData.goals_completed + 1))}
              >
                +
              </Button>
            </div>
          </div>

          {/* 今日笔记 */}
          <div className="mt-6 space-y-3">
            <label className="text-sm font-medium text-gray-700">今日感悟</label>
            <Textarea
              placeholder="记录今天的感受、收获或需要改进的地方..."
              value={todayData.notes}
              onChange={(e) => updateTodayData('notes', e.target.value)}
              rows={3}
            />
          </div>

          {/* 保存按钮 */}
          <div className="mt-6 flex justify-end">
            <Button 
              onClick={handleSave} 
              disabled={saving}
              className="bg-blue-500 hover:bg-blue-600"
            >
              <Save className="mr-2 h-4 w-4" />
              {saving ? '保存中...' : '保存今日追踪'}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* 本周总结 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <BarChart3 className="mr-2 h-5 w-5" />
            本周总结
          </CardTitle>
          <CardDescription>
            {trackingData.weekly_summary.week_start} 至 {trackingData.weekly_summary.week_end}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">{trackingData.weekly_summary.avg_mood.toFixed(1)}</div>
              <div className="text-sm text-gray-600">平均情绪</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-600">{trackingData.weekly_summary.avg_energy.toFixed(1)}</div>
              <div className="text-sm text-gray-600">平均精力</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">{trackingData.weekly_summary.avg_sleep.toFixed(1)}h</div>
              <div className="text-sm text-gray-600">平均睡眠</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">{trackingData.weekly_summary.goals_completion_rate}%</div>
              <div className="text-sm text-gray-600">目标完成率</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 最近记录 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <LineChart className="mr-2 h-5 w-5" />
            最近记录
          </CardTitle>
          <CardDescription>
            过去7天的追踪记录
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {trackingData.recent_entries.map((entry: any, index: number) => (
              <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex items-center gap-4">
                  <div className="text-sm font-medium text-gray-900">
                    {format(new Date(entry.date), 'MM/dd')}
                  </div>
                  <div className="flex items-center gap-2">
                    {getMoodIcon(entry.mood)}
                    <span className={`text-sm font-medium ${getMoodColor(entry.mood)}`}>
                      情绪 {entry.mood}/5
                    </span>
                  </div>
                  <div className="text-sm text-gray-600">
                    精力 {entry.energy}/5
                  </div>
                  <div className="text-sm text-gray-600">
                    睡眠 {entry.sleep_hours}h
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant="outline">
                    目标 {entry.goals_completed}/{entry.total_goals}
                  </Badge>
                  {entry.notes && (
                    <Badge variant="secondary">有笔记</Badge>
                  )}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
