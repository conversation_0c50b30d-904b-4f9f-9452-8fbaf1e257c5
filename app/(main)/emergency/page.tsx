'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  AlertTriangle,
  Phone,
  MessageCircle,
  Heart,
  Shield,
  Clock,
  Users,
  Headphones,
  Activity,
  Zap,
  Target,
  RefreshCw,
  Play,
  Pause,
  Volume2,
  VolumeX
} from 'lucide-react'
import { mockApiResponse } from '@/lib/mock-data'
import { Skeleton } from '@/components/ui/skeleton'

// 生成紧急求助数据
function generateEmergencyData() {
  const emergencyContacts = [
    {
      id: 'contact-1',
      name: '24小时心理热线',
      phone: '************',
      type: 'hotline',
      description: '专业心理咨询师提供24小时服务',
      availability: '24/7',
      response_time: '立即接听',
      speciality: '心理危机干预'
    },
    {
      id: 'contact-2',
      name: '戒色导师热线',
      phone: '************',
      type: 'mentor',
      description: '经验丰富的戒色导师提供指导',
      availability: '9:00-21:00',
      response_time: '5分钟内',
      speciality: '戒色指导'
    },
    {
      id: 'contact-3',
      name: '紧急伙伴支持',
      phone: '在线联系',
      type: 'buddy',
      description: '您的戒友伙伴随时为您提供支持',
      availability: '根据伙伴时间',
      response_time: '通常5-10分钟',
      speciality: '同伴支持'
    }
  ]

  const quickActions = [
    {
      id: 'breathing',
      name: '深呼吸练习',
      description: '4-7-8呼吸法，快速缓解紧张情绪',
      duration: '2分钟',
      effectiveness: 95,
      type: 'exercise'
    },
    {
      id: 'cold_shower',
      name: '冷水洗脸',
      description: '用冷水洗脸或洗澡，快速降低冲动',
      duration: '1分钟',
      effectiveness: 88,
      type: 'physical'
    },
    {
      id: 'pushups',
      name: '立即运动',
      description: '做20个俯卧撑或跳跃，转移注意力',
      duration: '3分钟',
      effectiveness: 92,
      type: 'exercise'
    },
    {
      id: 'meditation',
      name: '紧急冥想',
      description: '5分钟正念冥想，重新找回内心平静',
      duration: '5分钟',
      effectiveness: 90,
      type: 'mental'
    },
    {
      id: 'call_friend',
      name: '联系朋友',
      description: '立即给信任的朋友或家人打电话',
      duration: '10分钟',
      effectiveness: 85,
      type: 'social'
    },
    {
      id: 'go_outside',
      name: '外出散步',
      description: '立即离开当前环境，到户外走走',
      duration: '15分钟',
      effectiveness: 87,
      type: 'physical'
    }
  ]

  const motivationalMessages = [
    '这只是暂时的感觉，你有能力度过这个难关',
    '每一次抵制诱惑，都让你变得更强大',
    '想想你的目标，想想更好的自己',
    '你已经坚持了这么久，不要在这里放弃',
    '困难时刻正是证明你意志力的时候',
    '深呼吸，这个冲动会过去的',
    '你比你想象的更强大',
    '选择健康的生活方式，你值得拥有',
    '每一次正确的选择都在塑造更好的你',
    '相信自己，你一定可以做到'
  ]

  return {
    emergencyContacts,
    quickActions,
    motivationalMessages
  }
}

export default function EmergencyPage() {
  const [emergencyData, setEmergencyData] = useState<any>(null)
  const [currentMessage, setCurrentMessage] = useState<string>('')
  const [breathingActive, setBreathingActive] = useState(false)
  const [breathingPhase, setBreathingPhase] = useState<'inhale' | 'hold' | 'exhale'>('inhale')
  const [breathingCount, setBreathingCount] = useState(4)
  const [loading, setLoading] = useState(false)

  const loadEmergencyData = async () => {
    setLoading(true)
    try {
      const response = await mockApiResponse(generateEmergencyData())
      const data = response.data
      setEmergencyData(data)
      setCurrentMessage(data.motivationalMessages[0])
    } catch (error) {
      console.error('加载紧急求助数据失败:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    loadEmergencyData()
  }, [])

  useEffect(() => {
    let interval: NodeJS.Timeout
    if (breathingActive) {
      interval = setInterval(() => {
        setBreathingCount(prev => {
          if (prev <= 1) {
            if (breathingPhase === 'inhale') {
              setBreathingPhase('hold')
              return 7
            } else if (breathingPhase === 'hold') {
              setBreathingPhase('exhale')
              return 8
            } else {
              setBreathingPhase('inhale')
              return 4
            }
          }
          return prev - 1
        })
      }, 1000)
    }
    return () => clearInterval(interval)
  }, [breathingActive, breathingPhase])

  const startBreathing = () => {
    setBreathingActive(true)
    setBreathingPhase('inhale')
    setBreathingCount(4)
  }

  const stopBreathing = () => {
    setBreathingActive(false)
  }

  const shuffleMessage = () => {
    if (emergencyData?.motivationalMessages) {
      const randomMessage = emergencyData.motivationalMessages[
        Math.floor(Math.random() * emergencyData.motivationalMessages.length)
      ]
      setCurrentMessage(randomMessage)
    }
  }

  const getActionIcon = (type: string) => {
    switch (type) {
      case 'exercise': return Activity
      case 'physical': return Zap
      case 'mental': return Heart
      case 'social': return Users
      default: return Target
    }
  }

  const getContactIcon = (type: string) => {
    switch (type) {
      case 'hotline': return Phone
      case 'mentor': return Headphones
      case 'buddy': return Users
      default: return MessageCircle
    }
  }

  if (!emergencyData) {
    return (
      <div className="space-y-6">
        {/* 紧急警告骨架 */}
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center space-x-3">
            <Skeleton className="h-6 w-6" />
            <div className="flex-1 space-y-2">
              <Skeleton className="h-6 w-48" />
              <Skeleton className="h-4 w-96" />
            </div>
          </div>
        </div>

        {/* 页面标题骨架 */}
        <div className="text-center space-y-2">
          <Skeleton className="h-8 w-32 mx-auto" />
          <Skeleton className="h-4 w-64 mx-auto" />
        </div>

        {/* 紧急联系人骨架 */}
        <Card>
          <CardHeader>
            <Skeleton className="h-6 w-24" />
            <Skeleton className="h-4 w-48" />
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {[...Array(4)].map((_, i) => (
                <div key={i} className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex items-center space-x-3">
                    <Skeleton className="h-10 w-10 rounded-full" />
                    <div className="space-y-1">
                      <Skeleton className="h-4 w-24" />
                      <Skeleton className="h-3 w-20" />
                    </div>
                  </div>
                  <Skeleton className="h-8 w-16" />
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* 即时帮助工具骨架 */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* 呼吸练习骨架 */}
          <Card>
            <CardHeader>
              <Skeleton className="h-6 w-20" />
              <Skeleton className="h-4 w-40" />
            </CardHeader>
            <CardContent className="text-center space-y-4">
              <Skeleton className="h-32 w-32 rounded-full mx-auto" />
              <Skeleton className="h-6 w-24 mx-auto" />
              <Skeleton className="h-10 w-20 mx-auto" />
            </CardContent>
          </Card>

          {/* 励志信息骨架 */}
          <Card>
            <CardHeader>
              <Skeleton className="h-6 w-20" />
              <Skeleton className="h-4 w-40" />
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="text-center p-6 bg-blue-50 rounded-lg">
                <Skeleton className="h-6 w-48 mx-auto mb-2" />
                <Skeleton className="h-4 w-32 mx-auto" />
              </div>
              <Skeleton className="h-10 w-24 mx-auto" />
            </CardContent>
          </Card>
        </div>

        {/* 快速行动指南骨架 */}
        <Card>
          <CardHeader>
            <Skeleton className="h-6 w-32" />
            <Skeleton className="h-4 w-48" />
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {[...Array(6)].map((_, i) => (
                <div key={i} className="p-4 border rounded-lg hover:bg-gray-50 cursor-pointer">
                  <div className="flex items-center space-x-3 mb-2">
                    <Skeleton className="h-5 w-5" />
                    <Skeleton className="h-5 w-24" />
                  </div>
                  <Skeleton className="h-4 w-full mb-1" />
                  <Skeleton className="h-4 w-3/4" />
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* 专业资源骨架 */}
        <Card>
          <CardHeader>
            <Skeleton className="h-6 w-20" />
            <Skeleton className="h-4 w-48" />
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {[...Array(3)].map((_, i) => (
                <div key={i} className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex items-center space-x-3">
                    <Skeleton className="h-8 w-8" />
                    <div className="space-y-1">
                      <Skeleton className="h-4 w-32" />
                      <Skeleton className="h-3 w-24" />
                    </div>
                  </div>
                  <Skeleton className="h-8 w-16" />
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* 紧急警告 */}
      <Card className="border-red-200 bg-red-50">
        <CardContent className="p-6">
          <div className="flex items-center mb-4">
            <AlertTriangle className="h-8 w-8 text-red-500 mr-3" />
            <div>
              <h1 className="text-2xl font-bold text-red-900">紧急求助</h1>
              <p className="text-red-700">感到冲动？立即采取行动，你不是一个人在战斗</p>
            </div>
          </div>
          
          {/* 励志消息 */}
          <div className="bg-white p-4 rounded-lg border border-red-200 mb-4">
            <div className="flex items-center justify-between mb-2">
              <Shield className="h-5 w-5 text-red-500" />
              <Button size="sm" variant="ghost" onClick={shuffleMessage}>
                <RefreshCw className="h-4 w-4" />
              </Button>
            </div>
            <p className="text-lg font-medium text-gray-900 text-center">
              {currentMessage}
            </p>
          </div>

          {/* 呼吸练习 */}
          <div className="bg-white p-4 rounded-lg border border-red-200">
            <div className="text-center">
              <h3 className="font-medium text-gray-900 mb-2">4-7-8 呼吸法</h3>
              {breathingActive ? (
                <div className="space-y-4">
                  <div className="text-6xl font-bold text-blue-600">
                    {breathingCount}
                  </div>
                  <div className="text-lg text-gray-700">
                    {breathingPhase === 'inhale' && '深吸气...'}
                    {breathingPhase === 'hold' && '屏住呼吸...'}
                    {breathingPhase === 'exhale' && '慢慢呼气...'}
                  </div>
                  <Button onClick={stopBreathing} variant="outline">
                    <Pause className="mr-2 h-4 w-4" />
                    停止
                  </Button>
                </div>
              ) : (
                <div className="space-y-4">
                  <p className="text-gray-600">吸气4秒 → 屏气7秒 → 呼气8秒</p>
                  <Button onClick={startBreathing} className="bg-blue-500 hover:bg-blue-600">
                    <Play className="mr-2 h-4 w-4" />
                    开始呼吸练习
                  </Button>
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 紧急联系方式 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center text-red-600">
            <Phone className="mr-2 h-5 w-5" />
            紧急联系方式
          </CardTitle>
          <CardDescription>
            专业人士随时为您提供帮助
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {emergencyData.emergencyContacts.map((contact: any) => {
              const IconComponent = getContactIcon(contact.type)
              return (
                <div key={contact.id} className="p-4 border rounded-lg hover:shadow-md transition-shadow">
                  <div className="flex items-center gap-3 mb-3">
                    <div className="w-10 h-10 bg-red-100 rounded-lg flex items-center justify-center">
                      <IconComponent className="h-5 w-5 text-red-600" />
                    </div>
                    <div>
                      <h4 className="font-medium text-gray-900">{contact.name}</h4>
                      <p className="text-sm text-gray-600">{contact.speciality}</p>
                    </div>
                  </div>
                  <p className="text-sm text-gray-600 mb-3">{contact.description}</p>
                  <div className="space-y-2 mb-4">
                    <div className="flex justify-between text-xs">
                      <span className="text-gray-500">服务时间:</span>
                      <span className="font-medium">{contact.availability}</span>
                    </div>
                    <div className="flex justify-between text-xs">
                      <span className="text-gray-500">响应时间:</span>
                      <span className="font-medium">{contact.response_time}</span>
                    </div>
                  </div>
                  <Button className="w-full bg-red-500 hover:bg-red-600">
                    <Phone className="mr-2 h-4 w-4" />
                    {contact.phone}
                  </Button>
                </div>
              )
            })}
          </div>
        </CardContent>
      </Card>

      {/* 快速行动 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Zap className="mr-2 h-5 w-5 text-orange-500" />
            立即行动
          </CardTitle>
          <CardDescription>
            选择一个行动，立即转移注意力
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {emergencyData.quickActions.map((action: any) => {
              const IconComponent = getActionIcon(action.type)
              return (
                <div key={action.id} className="p-4 border rounded-lg hover:shadow-md transition-shadow cursor-pointer hover:border-orange-300">
                  <div className="flex items-center gap-3 mb-3">
                    <div className="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center">
                      <IconComponent className="h-5 w-5 text-orange-600" />
                    </div>
                    <div>
                      <h4 className="font-medium text-gray-900">{action.name}</h4>
                      <div className="flex items-center gap-2">
                        <Badge variant="outline" className="text-xs">{action.duration}</Badge>
                        <Badge variant="secondary" className="text-xs">效果 {action.effectiveness}%</Badge>
                      </div>
                    </div>
                  </div>
                  <p className="text-sm text-gray-600 mb-3">{action.description}</p>
                  <Button className="w-full" variant="outline">
                    立即开始
                  </Button>
                </div>
              )
            })}
          </div>
        </CardContent>
      </Card>

      {/* 紧急提醒 */}
      <Card className="border-yellow-200 bg-yellow-50">
        <CardContent className="p-6 text-center">
          <Clock className="h-12 w-12 text-yellow-600 mx-auto mb-4" />
          <h3 className="text-lg font-bold text-yellow-900 mb-2">记住：冲动是暂时的</h3>
          <p className="text-yellow-800 mb-4">
            大多数冲动在15-20分钟内会自然消退。坚持住，使用上面的方法，你一定可以度过这个难关。
          </p>
          <div className="flex justify-center gap-4">
            <Button variant="outline" className="border-yellow-300">
              <Heart className="mr-2 h-4 w-4" />
              我感觉好多了
            </Button>
            <Button variant="outline" className="border-yellow-300">
              <MessageCircle className="mr-2 h-4 w-4" />
              继续寻求帮助
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
