'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { 
  Heart,
  Brain,
  Activity,
  Moon,
  Zap,
  TrendingUp,
  TrendingDown,
  Calendar,
  BarChart3,
  PieChart,
  LineChart,
  RefreshCw,
  Plus,
  CheckCircle,
  AlertCircle,
  Info
} from 'lucide-react'
import { mockApiResponse } from '@/lib/mock-data'
import { Skeleton } from '@/components/ui/skeleton'
import Link from 'next/link'
import { format, subDays } from 'date-fns'

// 生成健康监测数据
function generateHealthData() {
  const healthMetrics = {
    overall_score: Math.floor(Math.random() * 30) + 70, // 70-100
    physical_health: {
      score: Math.floor(Math.random() * 25) + 75,
      sleep_quality: Math.floor(Math.random() * 3) + 3, // 3-5
      energy_level: Math.floor(Math.random() * 3) + 3,
      physical_symptoms: Math.floor(Math.random() * 3) + 1,
      exercise_frequency: Math.floor(Math.random() * 4) + 3
    },
    mental_health: {
      score: Math.floor(Math.random() * 25) + 75,
      mood_stability: Math.floor(Math.random() * 3) + 3,
      anxiety_level: Math.floor(Math.random() * 3) + 2,
      concentration: Math.floor(Math.random() * 3) + 3,
      self_confidence: Math.floor(Math.random() * 3) + 3
    },
    social_health: {
      score: Math.floor(Math.random() * 25) + 70,
      social_interaction: Math.floor(Math.random() * 3) + 3,
      relationship_quality: Math.floor(Math.random() * 3) + 3,
      communication_skills: Math.floor(Math.random() * 3) + 3
    },
    recovery_indicators: {
      urge_frequency: Math.floor(Math.random() * 3) + 1, // 1-3 (越低越好)
      urge_intensity: Math.floor(Math.random() * 3) + 1,
      coping_ability: Math.floor(Math.random() * 3) + 3, // 3-5 (越高越好)
      relapse_risk: Math.floor(Math.random() * 3) + 1 // 1-3 (越低越好)
    }
  }

  // 生成历史趋势数据
  const historyData = []
  for (let i = 29; i >= 0; i--) {
    const date = subDays(new Date(), i)
    historyData.push({
      date: format(date, 'yyyy-MM-dd'),
      overall_score: Math.floor(Math.random() * 20) + 70 + (29 - i) * 0.5, // 逐渐上升趋势
      physical_health: Math.floor(Math.random() * 20) + 70 + (29 - i) * 0.3,
      mental_health: Math.floor(Math.random() * 20) + 70 + (29 - i) * 0.4,
      social_health: Math.floor(Math.random() * 20) + 65 + (29 - i) * 0.3,
      mood: Math.floor(Math.random() * 3) + 3,
      energy: Math.floor(Math.random() * 3) + 3,
      sleep: Math.floor(Math.random() * 3) + 3
    })
  }

  return { healthMetrics, historyData }
}

// 生成健康量表数据
function generateHealthAssessments() {
  return [
    {
      id: 'phq9',
      name: 'PHQ-9 抑郁量表',
      description: '评估抑郁症状的严重程度',
      category: '心理健康',
      questions_count: 9,
      estimated_time: 5,
      last_completed: subDays(new Date(), 7).toISOString(),
      score: 8,
      max_score: 27,
      interpretation: '轻度抑郁',
      status: 'completed'
    },
    {
      id: 'gad7',
      name: 'GAD-7 焦虑量表',
      description: '评估广泛性焦虑障碍症状',
      category: '心理健康',
      questions_count: 7,
      estimated_time: 3,
      last_completed: subDays(new Date(), 7).toISOString(),
      score: 6,
      max_score: 21,
      interpretation: '轻度焦虑',
      status: 'completed'
    },
    {
      id: 'psqi',
      name: 'PSQI 睡眠质量量表',
      description: '评估睡眠质量和睡眠障碍',
      category: '身体健康',
      questions_count: 19,
      estimated_time: 8,
      last_completed: subDays(new Date(), 14).toISOString(),
      score: 7,
      max_score: 21,
      interpretation: '睡眠质量较差',
      status: 'completed'
    },
    {
      id: 'whoqol',
      name: 'WHO生活质量量表',
      description: '评估整体生活质量和幸福感',
      category: '综合评估',
      questions_count: 26,
      estimated_time: 10,
      last_completed: null,
      score: null,
      max_score: 130,
      interpretation: null,
      status: 'pending'
    },
    {
      id: 'self_efficacy',
      name: '自我效能感量表',
      description: '评估个人应对困难的信心',
      category: '心理健康',
      questions_count: 10,
      estimated_time: 5,
      last_completed: null,
      score: null,
      max_score: 40,
      interpretation: null,
      status: 'pending'
    }
  ]
}

export default function HealthMonitoringPage() {
  const [healthData, setHealthData] = useState<any>(null)
  const [assessments, setAssessments] = useState<any[]>([])
  const [loading, setLoading] = useState(false)
  const [selectedPeriod, setSelectedPeriod] = useState<'7d' | '30d' | '90d'>('30d')

  // 根据时间区间生成不同的健康数据
  const generateHealthDataByPeriod = (period: '7d' | '30d' | '90d') => {
    const baseData = generateHealthData()

    // 根据时间区间调整数据点数量和趋势
    const dataPoints = period === '7d' ? 7 : period === '30d' ? 30 : 90
    const trendMultiplier = period === '7d' ? 0.8 : period === '30d' ? 1.0 : 1.2

    // 生成趋势数据
    const generateTrendData = (baseValue: number, variance: number = 10) => {
      const data = []
      for (let i = 0; i < dataPoints; i++) {
        const trend = Math.sin((i / dataPoints) * Math.PI * 2) * variance * trendMultiplier
        const randomVariation = (Math.random() - 0.5) * variance * 0.5
        const value = Math.max(0, Math.min(100, baseValue + trend + randomVariation))
        data.push(Math.round(value))
      }
      return data
    }

    // 从healthMetrics中提取基础数据
    const baseMood = baseData.healthMetrics.mental_health.mood_stability * 20
    const baseEnergy = baseData.healthMetrics.physical_health.energy_level * 20
    const baseSleep = baseData.healthMetrics.physical_health.sleep_quality * 20
    const baseStress = baseData.healthMetrics.mental_health.anxiety_level * 25
    const baseFocus = baseData.healthMetrics.mental_health.concentration * 20

    return {
      ...baseData,
      period,
      trends: {
        mood: generateTrendData(baseMood, 15),
        energy: generateTrendData(baseEnergy, 12),
        sleep: generateTrendData(baseSleep, 10),
        stress: generateTrendData(100 - baseStress, 20).map(v => 100 - v),
        focus: generateTrendData(baseFocus, 18)
      },
      // 调整当前统计数据
      currentStats: {
        mood: period === '7d' ? baseMood - 5 :
              period === '30d' ? baseMood :
              baseMood + 8,
        energy: period === '7d' ? baseEnergy - 3 :
                period === '30d' ? baseEnergy :
                baseEnergy + 6,
        sleep: period === '7d' ? baseSleep - 2 :
               period === '30d' ? baseSleep :
               baseSleep + 4,
        stress: period === '7d' ? baseStress + 8 :
                period === '30d' ? baseStress :
                baseStress - 5,
        focus: period === '7d' ? baseFocus - 4 :
               period === '30d' ? baseFocus :
               baseFocus + 7
      }
    }
  }

  const loadHealthData = async (period?: '7d' | '30d' | '90d') => {
    setLoading(true)
    try {
      const currentPeriod = period || selectedPeriod
      const [healthResponse, assessmentsResponse] = await Promise.all([
        mockApiResponse(generateHealthDataByPeriod(currentPeriod)),
        mockApiResponse(generateHealthAssessments())
      ])

      setHealthData(healthResponse.data)
      setAssessments(assessmentsResponse.data)
    } catch (error) {
      console.error('加载健康数据失败:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    loadHealthData()
  }, [])

  // 监听时间区间变化
  useEffect(() => {
    if (healthData) { // 只有在初始数据加载完成后才响应时间区间变化
      loadHealthData(selectedPeriod)
    }
  }, [selectedPeriod]) // eslint-disable-line react-hooks/exhaustive-deps

  const getScoreColor = (score: number, maxScore: number = 100) => {
    const percentage = (score / maxScore) * 100
    if (percentage >= 80) return 'text-green-600'
    if (percentage >= 60) return 'text-yellow-600'
    return 'text-red-600'
  }

  const getScoreBgColor = (score: number, maxScore: number = 100) => {
    const percentage = (score / maxScore) * 100
    if (percentage >= 80) return 'bg-green-100'
    if (percentage >= 60) return 'bg-yellow-100'
    return 'bg-red-100'
  }

  // 计算趋势变化百分比
  const calculateTrendChange = (trendData: number[]) => {
    if (!trendData || trendData.length < 2) return { change: 0, isPositive: true }

    const recent = trendData.slice(-7) // 最近7个数据点
    const earlier = trendData.slice(-14, -7) // 之前7个数据点

    if (earlier.length === 0) return { change: 0, isPositive: true }

    const recentAvg = recent.reduce((sum, val) => sum + val, 0) / recent.length
    const earlierAvg = earlier.reduce((sum, val) => sum + val, 0) / earlier.length

    const change = ((recentAvg - earlierAvg) / earlierAvg) * 100
    return {
      change: Math.abs(change),
      isPositive: change >= 0
    }
  }

  // 获取健康指标的趋势数据
  const getHealthTrends = () => {
    if (!healthData?.trends) return null

    const trends = healthData.trends
    const overallHealth = [...trends.mood, ...trends.energy, ...trends.sleep, ...trends.focus] as number[]
    const physicalHealth = [...trends.energy, ...trends.sleep] as number[]
    const mentalHealth = [...trends.mood, ...trends.focus] as number[]
    const stressHealth = trends.stress.map((v: number) => 100 - v) as number[] // 压力越低越好

    return {
      overall: calculateTrendChange(overallHealth),
      physical: calculateTrendChange(physicalHealth),
      mental: calculateTrendChange(mentalHealth),
      social: calculateTrendChange(stressHealth) // 用压力数据模拟社交健康
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed': return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'pending': return <AlertCircle className="h-4 w-4 text-yellow-500" />
      default: return <Info className="h-4 w-4 text-gray-500" />
    }
  }

  if (!healthData) {
    return (
      <div className="space-y-6">
        {/* 页面标题骨架 */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div className="space-y-2">
            <div className="flex items-center">
              <Skeleton className="w-6 h-6 mr-2" />
              <Skeleton className="h-8 w-40" />
            </div>
            <Skeleton className="h-4 w-72" />
          </div>
          <Skeleton className="h-10 w-24 mt-4 sm:mt-0" />
        </div>

        {/* 健康指标卡片骨架 */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[...Array(4)].map((_, i) => (
            <Card key={i} className="p-6">
              <div className="flex items-center justify-between mb-4">
                <Skeleton className="h-5 w-20" />
                <Skeleton className="w-5 h-5" />
              </div>
              <Skeleton className="h-8 w-16 mb-2" />
              <Skeleton className="h-3 w-24" />
            </Card>
          ))}
        </div>

        {/* 图表区域骨架 */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card className="p-6">
            <Skeleton className="h-6 w-32 mb-4" />
            <Skeleton className="h-64 w-full" />
          </Card>
          <Card className="p-6">
            <Skeleton className="h-6 w-32 mb-4" />
            <Skeleton className="h-64 w-full" />
          </Card>
        </div>

        {/* 建议列表骨架 */}
        <Card className="p-6">
          <Skeleton className="h-6 w-32 mb-4" />
          <div className="space-y-3">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="flex items-start space-x-3">
                <Skeleton className="w-5 h-5 mt-0.5" />
                <div className="flex-1 space-y-1">
                  <Skeleton className="h-4 w-full" />
                  <Skeleton className="h-3 w-3/4" />
                </div>
              </div>
            ))}
          </div>
        </Card>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 flex items-center">
            <Heart className="mr-2 h-6 w-6 text-red-500" />
            健康监测系统
          </h1>
          <p className="text-gray-600">全面监测身心健康状况，科学评估康复进展</p>
        </div>
        <Button
          onClick={() => loadHealthData()}
          disabled={loading}
          variant="gradient"
        >
          <RefreshCw className={`mr-2 h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
          {loading ? '更新中...' : '刷新数据'}
        </Button>
      </div>

      {/* 健康总览 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">综合健康分数</CardTitle>
            <Activity className="h-4 w-4 text-purple-500" />
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${getScoreColor(healthData.healthMetrics.overall_score)}`}>
              {healthData.healthMetrics.overall_score}
            </div>
            <p className="text-xs text-muted-foreground">
              较上周 +3.2
            </p>
            <Progress value={healthData.healthMetrics.overall_score} className="mt-2" />
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">身体健康</CardTitle>
            <Heart className="h-4 w-4 text-red-500" />
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${getScoreColor(healthData.healthMetrics.physical_health.score)}`}>
              {healthData.healthMetrics.physical_health.score}
            </div>
            <p className="text-xs text-muted-foreground">
              睡眠质量 {healthData.healthMetrics.physical_health.sleep_quality}/5
            </p>
            <Progress value={healthData.healthMetrics.physical_health.score} className="mt-2" />
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">心理健康</CardTitle>
            <Brain className="h-4 w-4 text-blue-500" />
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${getScoreColor(healthData.healthMetrics.mental_health.score)}`}>
              {healthData.healthMetrics.mental_health.score}
            </div>
            <p className="text-xs text-muted-foreground">
              情绪稳定性 {healthData.healthMetrics.mental_health.mood_stability}/5
            </p>
            <Progress value={healthData.healthMetrics.mental_health.score} className="mt-2" />
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">社交健康</CardTitle>
            <Zap className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${getScoreColor(healthData.healthMetrics.social_health.score)}`}>
              {healthData.healthMetrics.social_health.score}
            </div>
            <p className="text-xs text-muted-foreground">
              人际关系质量 {healthData.healthMetrics.social_health.relationship_quality}/5
            </p>
            <Progress value={healthData.healthMetrics.social_health.score} className="mt-2" />
          </CardContent>
        </Card>
      </div>

      {/* 康复指标 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <TrendingUp className="mr-2 h-5 w-5" />
            康复关键指标
          </CardTitle>
          <CardDescription>
            监测戒色康复过程中的关键健康指标
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div className="text-center">
              <div className="text-3xl font-bold text-green-600 mb-2">
                {6 - healthData.healthMetrics.recovery_indicators.urge_frequency}/5
              </div>
              <div className="text-sm text-gray-600 mb-2">冲动控制</div>
              <Progress value={(6 - healthData.healthMetrics.recovery_indicators.urge_frequency) * 20} />
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-blue-600 mb-2">
                {healthData.healthMetrics.recovery_indicators.coping_ability}/5
              </div>
              <div className="text-sm text-gray-600 mb-2">应对能力</div>
              <Progress value={healthData.healthMetrics.recovery_indicators.coping_ability * 20} />
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-purple-600 mb-2">
                {6 - healthData.healthMetrics.recovery_indicators.urge_intensity}/5
              </div>
              <div className="text-sm text-gray-600 mb-2">冲动强度</div>
              <Progress value={(6 - healthData.healthMetrics.recovery_indicators.urge_intensity) * 20} />
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-orange-600 mb-2">
                {6 - healthData.healthMetrics.recovery_indicators.relapse_risk}/5
              </div>
              <div className="text-sm text-gray-600 mb-2">复发风险</div>
              <Progress value={(6 - healthData.healthMetrics.recovery_indicators.relapse_risk) * 20} />
            </div>
          </div>
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 健康量表 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <BarChart3 className="mr-2 h-5 w-5" />
              标准化健康量表
            </CardTitle>
            <CardDescription>
              使用科学量表评估各项健康指标
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {assessments.map((assessment) => (
                <div key={assessment.id} className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      {getStatusIcon(assessment.status)}
                      <h4 className="font-medium">{assessment.name}</h4>
                      <Badge variant="outline" className="text-xs">
                        {assessment.category}
                      </Badge>
                    </div>
                    <p className="text-sm text-gray-600 mb-2">{assessment.description}</p>
                    <div className="flex items-center gap-4 text-xs text-gray-500">
                      <span>{assessment.questions_count} 题</span>
                      <span>{assessment.estimated_time} 分钟</span>
                      {assessment.last_completed && (
                        <span>上次完成: {format(new Date(assessment.last_completed), 'MM/dd')}</span>
                      )}
                    </div>
                    {assessment.score !== null && (
                      <div className="mt-2">
                        <div className="flex items-center gap-2">
                          <span className="text-sm font-medium">得分: {assessment.score}/{assessment.max_score}</span>
                          <Badge className={getScoreBgColor(assessment.score, assessment.max_score)}>
                            {assessment.interpretation}
                          </Badge>
                        </div>
                      </div>
                    )}
                  </div>
                  <Link href="/assessment">
                    <Button size="sm" variant={assessment.status === 'completed' ? 'outline' : 'default'}>
                      {assessment.status === 'completed' ? '重新测试' : '开始测试'}
                    </Button>
                  </Link>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* 健康趋势 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <LineChart className="mr-2 h-5 w-5" />
              健康趋势分析
            </CardTitle>
            <CardDescription>
              过去30天的健康指标变化趋势
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex gap-2">
                {['7d', '30d', '90d'].map(period => (
                  <Button
                    key={period}
                    variant={selectedPeriod === period ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setSelectedPeriod(period as any)}
                    disabled={loading}
                  >
                    {loading && selectedPeriod === period ? (
                      <>
                        <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-current mr-2"></div>
                        加载中...
                      </>
                    ) : (
                      period === '7d' ? '7天' : period === '30d' ? '30天' : '90天'
                    )}
                  </Button>
                ))}
              </div>
              
              {/* 简化的趋势展示 */}
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">综合健康</span>
                  <div className="flex items-center gap-2">
                    <TrendingUp className="h-4 w-4 text-green-500" />
                    <span className="text-sm font-medium text-green-600">+5.2%</span>
                  </div>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">身体健康</span>
                  <div className="flex items-center gap-2">
                    <TrendingUp className="h-4 w-4 text-green-500" />
                    <span className="text-sm font-medium text-green-600">+3.8%</span>
                  </div>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">心理健康</span>
                  <div className="flex items-center gap-2">
                    <TrendingUp className="h-4 w-4 text-green-500" />
                    <span className="text-sm font-medium text-green-600">+6.1%</span>
                  </div>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">社交健康</span>
                  <div className="flex items-center gap-2">
                    <TrendingDown className="h-4 w-4 text-red-500" />
                    <span className="text-sm font-medium text-red-600">-1.2%</span>
                  </div>
                </div>
              </div>

              <div className="mt-4 p-3 bg-blue-50 rounded-lg">
                <h4 className="font-medium text-blue-900 mb-1">健康建议</h4>
                <p className="text-sm text-blue-800">
                  您的整体健康状况呈上升趋势，建议继续保持良好的作息和运动习惯。
                  社交健康略有下降，可以考虑增加与朋友的互动。
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
