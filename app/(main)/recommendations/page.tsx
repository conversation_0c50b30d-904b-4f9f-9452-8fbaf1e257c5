'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  Sparkles,
  Clock,
  Target,
  TrendingUp,
  BookOpen,
  Play,
  Headphones,
  Dumbbell,
  Trophy,
  Star,
  ThumbsUp,
  Eye,
  RefreshCw
} from 'lucide-react'
import { 
  generateMockRecommendationContent,
  generateMockUserBehaviors,
  generateMockPersonalizedRecommendations,
  mockApiResponse 
} from '@/lib/mock-data'
import Link from 'next/link'

export default function RecommendationsPage() {
  const [recommendations, setRecommendations] = useState<any[]>([])
  const [contentItems, setContentItems] = useState<any[]>([])
  const [userBehaviors, setUserBehaviors] = useState<any[]>([])
  const [loading, setLoading] = useState(false)
  const [selectedCategory, setSelectedCategory] = useState<string>('all')

  const loadRecommendations = async () => {
    setLoading(true)
    try {
      const [recommendationsResponse, contentResponse, behaviorsResponse] = await Promise.all([
        mockApiResponse(generateMockPersonalizedRecommendations('current-user')),
        mockApiResponse(generateMockRecommendationContent()),
        mockApiResponse(generateMockUserBehaviors('current-user', 30))
      ])

      setRecommendations(recommendationsResponse.data)
      setContentItems(contentResponse.data)
      setUserBehaviors(behaviorsResponse.data)
    } catch (error) {
      console.error('加载推荐数据失败:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    loadRecommendations()
  }, [])

  const getContentById = (contentId: string) => {
    return contentItems.find(item => item.id === contentId)
  }

  const getIconByType = (type: string) => {
    switch (type) {
      case 'article': return BookOpen
      case 'video': return Play
      case 'audio': return Headphones
      case 'exercise': return Dumbbell
      case 'challenge': return Trophy
      default: return BookOpen
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'bg-red-100 text-red-800 border-red-200'
      case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'low': return 'bg-green-100 text-green-800 border-green-200'
      default: return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const filteredRecommendations = selectedCategory === 'all' 
    ? recommendations 
    : recommendations.filter(rec => {
        const content = getContentById(rec.content_id)
        return content?.category === selectedCategory
      })

  const categories = ['all', ...Array.from(new Set(contentItems.map(item => item.category)))]

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 flex items-center">
            <Sparkles className="mr-2 h-6 w-6 text-purple-500" />
            AI个性化推荐
          </h1>
          <p className="text-gray-600">基于您的行为和偏好，为您精心推荐最适合的内容</p>
        </div>
        <Button 
          onClick={loadRecommendations} 
          disabled={loading}
          variant="gradient"
        >
          <RefreshCw className={`mr-2 h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
          {loading ? '更新中...' : '刷新推荐'}
        </Button>
      </div>

      {/* 用户行为洞察 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">总浏览量</CardTitle>
            <Eye className="h-4 w-4 text-blue-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{userBehaviors.filter(b => b.action_type === 'view').length}</div>
            <p className="text-xs text-muted-foreground">
              最近30天
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">点赞数</CardTitle>
            <ThumbsUp className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{userBehaviors.filter(b => b.action_type === 'like').length}</div>
            <p className="text-xs text-muted-foreground">
              参与度 {Math.round((userBehaviors.filter(b => b.action_type === 'like').length / userBehaviors.length) * 100)}%
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">完成数</CardTitle>
            <Target className="h-4 w-4 text-purple-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{userBehaviors.filter(b => b.action_type === 'complete').length}</div>
            <p className="text-xs text-muted-foreground">
              完成率 {Math.round((userBehaviors.filter(b => b.action_type === 'complete').length / userBehaviors.filter(b => b.content_type === 'exercise' || b.content_type === 'challenge').length) * 100) || 0}%
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">推荐精度</CardTitle>
            <TrendingUp className="h-4 w-4 text-orange-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">92%</div>
            <p className="text-xs text-muted-foreground">
              AI匹配度
            </p>
          </CardContent>
        </Card>
      </div>

      {/* 分类筛选 */}
      <div className="flex flex-wrap gap-2">
        {categories.map(category => (
          <Button
            key={category}
            variant={selectedCategory === category ? "default" : "outline"}
            size="sm"
            onClick={() => setSelectedCategory(category)}
          >
            {category === 'all' ? '全部' : category}
          </Button>
        ))}
      </div>

      {/* 推荐内容列表 */}
      <div className="space-y-4">
        {filteredRecommendations.map((recommendation, index) => {
          const content = getContentById(recommendation.content_id)
          if (!content) return null

          const IconComponent = getIconByType(content.type)

          return (
            <Card key={recommendation.content_id} className="hover:shadow-md transition-shadow">
              <CardContent className="p-6">
                <>
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex items-start space-x-4 flex-1">
                      <div className="w-12 h-12 bg-gradient-to-br from-blue-100 to-purple-100 rounded-lg flex items-center justify-center">
                        <IconComponent className="h-6 w-6 text-blue-600" />
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-2">
                          <h3 className="font-semibold text-lg">
                            <Link href={`/article/${content.id}`} className="hover:underline">
                              {content.title}
                            </Link>
                          </h3>
                          <Badge className={getPriorityColor(recommendation.priority)}>
                            {recommendation.priority === 'high' ? '高优先级' : 
                             recommendation.priority === 'medium' ? '中优先级' : '低优先级'}
                          </Badge>
                        </div>
                        <div>
                          <p className="text-gray-600 mb-3">{content.description}</p>
                          
                          {/* 推荐原因 */}
                          <div className="flex flex-wrap gap-2 mb-3">
                            {recommendation.reasons.map((reason: string, idx: number) => (
                              <Badge key={idx} variant="secondary" className="text-xs">
                                {reason}
                              </Badge>
                            ))}
                          </div>

                          {/* 内容信息 */}
                          <div className="flex items-center gap-4 text-sm text-gray-500">
                            <div className="flex items-center gap-1">
                              <Clock className="h-4 w-4" />
                              {content.estimated_duration}分钟
                            </div>
                            <div className="flex items-center gap-1">
                              <Star className="h-4 w-4" />
                              难度 {content.difficulty_level}/5
                            </div>
                            <div className="flex items-center gap-1">
                              <TrendingUp className="h-4 w-4" />
                              匹配度 {Math.round(recommendation.score * 100)}%
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* 推荐分数 */}
                    <div className="text-right">
                      <div className="text-2xl font-bold text-purple-600">
                        {Math.round(recommendation.score * 100)}
                      </div>
                      <div className="text-xs text-gray-500">推荐分数</div>
                    </div>
                  </div>

                  {/* 个性化因子 */}
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 p-4 bg-gray-50 rounded-lg">
                    <div className="text-center">
                      <div className="text-sm font-medium text-gray-900">
                        {Math.round(recommendation.personalization_factors.behavior_match * 100)}%
                      </div>
                      <div className="text-xs text-gray-500">行为匹配</div>
                    </div>
                    <div className="text-center">
                      <div className="text-sm font-medium text-gray-900">
                        {Math.round(recommendation.personalization_factors.preference_match * 100)}%
                      </div>
                      <div className="text-xs text-gray-500">偏好匹配</div>
                    </div>
                    <div className="text-center">
                      <div className="text-sm font-medium text-gray-900">
                        {Math.round(recommendation.personalization_factors.context_match * 100)}%
                      </div>
                      <div className="text-xs text-gray-500">情境匹配</div>
                    </div>
                    <div className="text-center">
                      <div className="text-sm font-medium text-gray-900">
                        {Math.round(recommendation.personalization_factors.novelty_score * 100)}%
                      </div>
                      <div className="text-xs text-gray-500">新颖性</div>
                    </div>
                  </div>

                  {/* 时机建议 */}
                  {recommendation.timing_suggestion && (
                    <div className="mt-4 p-3 bg-blue-50 rounded-lg">
                      <div className="flex items-center gap-2 text-blue-800">
                        <Clock className="h-4 w-4" />
                        <span className="text-sm font-medium">最佳时机：{recommendation.timing_suggestion}</span>
                      </div>
                    </div>
                  )}

                  {/* 操作按钮 */}
                  <div className="flex gap-2 mt-4">
                    <Link href={`/article/${content.id}`} className="flex-1">
                      <Button size="sm" className="w-full">
                        立即查看
                      </Button>
                    </Link>
                    <Button size="sm" variant="outline">
                      稍后阅读
                    </Button>
                    <Button size="sm" variant="ghost">
                      不感兴趣
                    </Button>
                  </div>
                </>
              </CardContent>
            </Card>
          )
        })}
      </div>

      {filteredRecommendations.length === 0 && !loading && (
        <Card>
          <CardContent className="text-center py-12">
            <Sparkles className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">暂无推荐内容</h3>
            <p className="text-gray-500">请尝试更换分类或刷新推荐</p>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
