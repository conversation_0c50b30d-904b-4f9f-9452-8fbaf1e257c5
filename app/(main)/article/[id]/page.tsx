'use client'

import { useParams } from 'next/navigation'
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import Link from 'next/link'
import { mockPosts } from '@/lib/mock-data'
import { formatRelativeDate } from '@/lib/utils'
import { ArrowLeft, MessageSquare, ThumbsUp } from 'lucide-react'
import ShareDialog from '@/components/article/share-dialog'

export default function ArticleDetailPage() {
  const params = useParams()
  const { id } = params

  // 在真实应用中，这里会根据 id 从 API 获取数据
  const post = mockPosts.find(p => p.id === id) || mockPosts[0]

  if (!post) {
    return (
      <div className="container mx-auto p-4 text-center">
        <p>文章未找到</p>
        <Link href="/community" className="mt-4 inline-block">
          <Button>返回社区</Button>
        </Link>
      </div>
    )
  }

  return (
    <div className="container mx-auto max-w-3xl p-4">
      <Link href="/community" className="inline-flex items-center text-sm text-gray-600 hover:text-gray-900 mb-4">
        <ArrowLeft className="mr-2 h-4 w-4" />
        返回社区
      </Link>
      
      <Card className="overflow-hidden">
        {post.cover_image_url && (
          <img 
            src={post.cover_image_url} 
            alt={post.title} 
            className="w-full h-64 object-cover"
          />
        )}
        
        <CardHeader className="border-b">
          <CardTitle className="text-3xl font-bold tracking-tight">{post.title}</CardTitle>
          <CardDescription className="flex items-center space-x-4 pt-2 text-sm text-gray-500">
            <>
              <div className="flex items-center space-x-2">
                <Avatar className="h-8 w-8">
                  <AvatarFallback>{post.anonymous_id ? post.anonymous_id.charAt(0) : 'U'}</AvatarFallback>
                </Avatar>
                <span>{post.anonymous_id || '匿名用户'}</span>
              </div>
              <span>{formatRelativeDate(post.created_at)}</span>
              <span>{post.type}</span>
            </>
          </CardDescription>
        </CardHeader>
        
        <CardContent className="py-6">
          <div 
            className="prose prose-lg max-w-none dark:prose-invert"
            dangerouslySetInnerHTML={{ __html: post.content }}
          />
        </CardContent>
        
        <div className="border-t p-4 flex items-center justify-between text-gray-600">
          <div className="flex items-center space-x-6">
            <Button variant="ghost" size="sm" className="flex items-center space-x-2">
              <ThumbsUp className="h-5 w-5" />
              <span>{post.likes_count}</span>
            </Button>
            <Button variant="ghost" size="sm" className="flex items-center space-x-2">
              <MessageSquare className="h-5 w-5" />
              <span>{post.comments_count}</span>
            </Button>
          </div>
          <ShareDialog
            title={post.title}
            content={post.content}
            articleId={post.id}
            author={post.anonymous_id || '匿名用户'}
          />
        </div>
      </Card>
    </div>
  )
}
