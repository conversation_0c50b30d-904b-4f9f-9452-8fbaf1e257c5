'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import {
  RefreshCw,
  Users,
  MessageSquare,
  Award,
  TrendingUp,
  Calendar,
  Target,
  Zap,
  Activity,
  Filter,
  Eye,
  ArrowLeft,
  Heart,
  Share,
  Bookmark,
  ChevronLeft,
  ChevronRight
} from 'lucide-react'
import { 
  generateMockPosts, 
  generateMockLeaderboard, 
  generateMockAchievements,
  generateMockCommunityStats,
  generateMockUserActivity,
  mockApiResponse 
} from '@/lib/mock-data'

export default function DataDemoPage() {
  const [posts, setPosts] = useState<any[]>([])
  const [leaderboard, setLeaderboard] = useState<any>(null)
  const [achievements, setAchievements] = useState<any>(null)
  const [communityStats, setCommunityStats] = useState<any>(null)
  const [userActivity, setUserActivity] = useState<any[]>([])
  const [loading, setLoading] = useState(false)
  const [selectedPost, setSelectedPost] = useState<any>(null)
  const [selectedTag, setSelectedTag] = useState<string>('all')
  const [currentPostIndex, setCurrentPostIndex] = useState(0)

  // 可用的标签
  const availableTags = [
    { id: 'all', name: '全部', color: 'bg-gray-100 text-gray-800' },
    { id: 'share', name: '分享', color: 'bg-green-100 text-green-800' },
    { id: 'question', name: '求助', color: 'bg-blue-100 text-blue-800' },
    { id: 'support', name: '支持', color: 'bg-purple-100 text-purple-800' }
  ]

  const generateNewData = async () => {
    setLoading(true)
    try {
      const [
        postsResponse,
        leaderboardResponse,
        achievementsResponse,
        statsResponse,
        activityResponse
      ] = await Promise.all([
        mockApiResponse(generateMockPosts(10)),
        mockApiResponse(generateMockLeaderboard()),
        mockApiResponse(generateMockAchievements()),
        mockApiResponse(generateMockCommunityStats()),
        mockApiResponse(generateMockUserActivity(15))
      ])

      setPosts(postsResponse.data)
      setLeaderboard(leaderboardResponse.data)
      setAchievements(achievementsResponse.data)
      setCommunityStats(statsResponse.data)
      setUserActivity(activityResponse.data)
    } catch (error) {
      console.error('生成数据失败:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    generateNewData()
  }, [])

  // 过滤帖子
  const filteredPosts = selectedTag === 'all'
    ? posts
    : posts.filter(post => post.type === selectedTag)

  // 切换到下一个帖子
  const nextPost = () => {
    if (currentPostIndex < filteredPosts.length - 1) {
      setCurrentPostIndex(prev => prev + 1)
    }
  }

  // 切换到上一个帖子
  const prevPost = () => {
    if (currentPostIndex > 0) {
      setCurrentPostIndex(prev => prev - 1)
    }
  }

  // 重置帖子索引当标签改变时
  useEffect(() => {
    setCurrentPostIndex(0)
  }, [selectedTag])

  // 获取当前显示的帖子
  const currentPost = filteredPosts[currentPostIndex]

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">虚拟数据展示</h1>
          <p className="text-gray-600">展示动态生成的虚拟数据，每次刷新都会产生新的内容</p>
        </div>
        <Button 
          onClick={generateNewData} 
          disabled={loading}
          variant="gradient"
        >
          <RefreshCw className={`mr-2 h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
          {loading ? '生成中...' : '重新生成'}
        </Button>
      </div>

      {/* 社区统计概览 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">活跃用户</CardTitle>
            <Users className="h-4 w-4 text-blue-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{communityStats?.activeUsers?.toLocaleString() || '0'}</div>
            <p className="text-xs text-muted-foreground">
              本周新增 +{communityStats?.weeklyGrowth || 0}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">今日帖子</CardTitle>
            <MessageSquare className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{communityStats?.todayPosts || 0}</div>
            <p className="text-xs text-muted-foreground">
              评论 {communityStats?.todayComments || 0} 条
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">总互动</CardTitle>
            <TrendingUp className="h-4 w-4 text-purple-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{communityStats?.totalInteractions?.toLocaleString() || '0'}</div>
            <p className="text-xs text-muted-foreground">
              点赞和评论
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">总用户</CardTitle>
            <Target className="h-4 w-4 text-orange-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{communityStats?.totalUsers?.toLocaleString() || '0'}</div>
            <p className="text-xs text-muted-foreground">
              持续增长中
            </p>
          </CardContent>
        </Card>
      </div>

      {/* 热门标签 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Zap className="mr-2 h-5 w-5" />
            热门标签
          </CardTitle>
          <CardDescription>
            社区中最受欢迎的话题标签
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-2">
            {communityStats?.popularTags?.map((tag: any, index: number) => (
              <div
                key={index}
                className="px-3 py-1 bg-gradient-to-r from-blue-100 to-purple-100 text-blue-800 rounded-full text-sm font-medium"
              >
                #{tag.tag} ({tag.count})
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 最新帖子 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <div className="flex items-center">
                <MessageSquare className="mr-2 h-5 w-5" />
                最新帖子
              </div>
              <div className="flex items-center gap-2">
                <Filter className="h-4 w-4 text-gray-500" />
                <span className="text-sm text-gray-500">
                  {filteredPosts.length} 个帖子
                </span>
              </div>
            </CardTitle>
            <CardDescription>
              社区中最新发布的帖子
            </CardDescription>
          </CardHeader>
          <CardContent>
            {/* 标签过滤 */}
            <div className="mb-4">
              <div className="flex flex-wrap gap-2">
                {availableTags.map((tag) => (
                  <button
                    key={tag.id}
                    onClick={() => setSelectedTag(tag.id)}
                    className={`px-2 py-1 rounded-full text-xs transition-colors ${
                      selectedTag === tag.id
                        ? 'bg-blue-500 text-white'
                        : tag.color + ' hover:opacity-80'
                    }`}
                  >
                    {tag.name}
                  </button>
                ))}
              </div>
            </div>

            {/* 帖子切换器 */}
            {filteredPosts.length > 0 && (
              <div className="mb-4">
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center gap-2">
                    <button
                      onClick={prevPost}
                      disabled={currentPostIndex === 0}
                      className="p-1 rounded border hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      <ChevronLeft className="h-3 w-3" />
                    </button>
                    <span className="text-xs text-gray-600">
                      {currentPostIndex + 1} / {filteredPosts.length}
                    </span>
                    <button
                      onClick={nextPost}
                      disabled={currentPostIndex === filteredPosts.length - 1}
                      className="p-1 rounded border hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      <ChevronRight className="h-3 w-3" />
                    </button>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setSelectedPost(currentPost)}
                    disabled={!currentPost}
                  >
                    <Eye className="mr-1 h-3 w-3" />
                    详情
                  </Button>
                </div>
              </div>
            )}

            <div className="space-y-4">
              {filteredPosts.slice(0, 5).map((post: any, index: number) => (
                <div
                  key={post.id}
                  className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                    index === currentPostIndex ? 'border-blue-300 bg-blue-50' : 'hover:bg-gray-50'
                  }`}
                  onClick={() => setCurrentPostIndex(index)}
                >
                  <div className="flex items-start justify-between mb-2">
                    <h4 className="font-medium text-sm line-clamp-1">{post.title}</h4>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                      post.type === 'share' ? 'bg-green-100 text-green-800' :
                      post.type === 'question' ? 'bg-blue-100 text-blue-800' :
                      'bg-purple-100 text-purple-800'
                    }`}>
                      {post.type === 'share' ? '分享' :
                       post.type === 'question' ? '求助' : '支持'}
                    </span>
                  </div>
                  <p className="text-xs text-gray-600 line-clamp-2 mb-2">
                    {post.content}
                  </p>
                  <div className="flex items-center justify-between text-xs text-gray-500">
                    <span>{post.anonymous_id}</span>
                    <div className="flex items-center gap-3">
                      <span>👍 {post.likes_count}</span>
                      <span>💬 {post.comments_count}</span>
                    </div>
                  </div>
                </div>
              ))}

              {filteredPosts.length === 0 && (
                <div className="text-center py-4 text-gray-500">
                  <MessageSquare className="mx-auto h-8 w-8 text-gray-300 mb-2" />
                  <p className="text-sm">没有找到匹配的帖子</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* 排行榜预览 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Award className="mr-2 h-5 w-5" />
              排行榜
            </CardTitle>
            <CardDescription>
              连续天数和贡献排行
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <h4 className="font-medium text-sm mb-2 text-orange-600">🔥 连续天数 TOP 3</h4>
                <div className="space-y-2">
                  {leaderboard?.streakLeaderboard?.slice(0, 3).map((user: any, index: number) => (
                    <div key={index} className="flex items-center justify-between text-sm">
                      <div className="flex items-center gap-2">
                        <span className={`w-5 h-5 rounded-full flex items-center justify-center text-xs font-bold text-white ${
                          index === 0 ? 'bg-yellow-500' :
                          index === 1 ? 'bg-gray-500' : 'bg-orange-500'
                        }`}>
                          {index + 1}
                        </span>
                        <span>{user.name}</span>
                      </div>
                      <span className="font-medium text-orange-600">{user.days}天</span>
                    </div>
                  ))}
                </div>
              </div>
              
              <div>
                <h4 className="font-medium text-sm mb-2 text-red-600">❤️ 贡献排行 TOP 3</h4>
                <div className="space-y-2">
                  {leaderboard?.contributionLeaderboard?.slice(0, 3).map((user: any, index: number) => (
                    <div key={index} className="flex items-center justify-between text-sm">
                      <div className="flex items-center gap-2">
                        <span className={`w-5 h-5 rounded-full flex items-center justify-center text-xs font-bold text-white ${
                          index === 0 ? 'bg-yellow-500' :
                          index === 1 ? 'bg-gray-500' : 'bg-orange-500'
                        }`}>
                          {index + 1}
                        </span>
                        <span>{user.name}</span>
                      </div>
                      <span className="font-medium text-red-600">{user.points}分</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 用户活动时间线 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Activity className="mr-2 h-5 w-5" />
            用户活动时间线
          </CardTitle>
          <CardDescription>
            最近的用户活动记录
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {userActivity.slice(0, 8).map((activity: any) => (
              <div key={activity.id} className="flex items-center gap-3 p-2 rounded-lg hover:bg-gray-50">
                <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                  <Calendar className="h-4 w-4 text-blue-600" />
                </div>
                <div className="flex-1">
                  <p className="text-sm font-medium">{activity.label}</p>
                  <p className="text-xs text-gray-500">{activity.description}</p>
                </div>
                <div className="text-xs text-gray-400">
                  +{activity.points}分
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* 帖子详情模态框 */}
      {selectedPost && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div className="sticky top-0 bg-white border-b p-4 flex items-center justify-between">
              <h2 className="text-lg font-semibold text-gray-900">帖子详情</h2>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setSelectedPost(null)}
              >
                <ArrowLeft className="mr-2 h-4 w-4" />
                返回
              </Button>
            </div>
            <div className="p-6">
              <div className="space-y-6">
                {/* 帖子头部 */}
                <div>
                  <div className="flex items-start justify-between mb-3">
                    <h1 className="text-xl font-bold text-gray-900">{selectedPost.title}</h1>
                    <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                      selectedPost.type === 'share' ? 'bg-green-100 text-green-800' :
                      selectedPost.type === 'question' ? 'bg-blue-100 text-blue-800' :
                      'bg-purple-100 text-purple-800'
                    }`}>
                      {selectedPost.type === 'share' ? '分享' :
                       selectedPost.type === 'question' ? '求助' : '支持'}
                    </span>
                  </div>
                  <div className="flex items-center gap-4 text-sm text-gray-500 mb-4">
                    <span>作者: {selectedPost.anonymous_id}</span>
                    <span>发布时间: {selectedPost.created_at}</span>
                  </div>
                </div>

                {/* 帖子内容 */}
                <div className="prose max-w-none">
                  <p className="text-gray-700 leading-relaxed whitespace-pre-wrap">
                    {selectedPost.content}
                  </p>
                </div>

                {/* 互动数据 */}
                <div className="flex items-center gap-6 py-4 border-t border-b">
                  <div className="flex items-center gap-2">
                    <Heart className="h-5 w-5 text-red-500" />
                    <span className="font-medium">{selectedPost.likes_count}</span>
                    <span className="text-gray-500">点赞</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <MessageSquare className="h-5 w-5 text-blue-500" />
                    <span className="font-medium">{selectedPost.comments_count}</span>
                    <span className="text-gray-500">评论</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Share className="h-5 w-5 text-green-500" />
                    <span className="font-medium">{selectedPost.shares_count || 0}</span>
                    <span className="text-gray-500">分享</span>
                  </div>
                </div>

                {/* 操作按钮 */}
                <div className="flex gap-3">
                  <Button variant="outline" className="flex-1">
                    <Heart className="mr-2 h-4 w-4" />
                    点赞
                  </Button>
                  <Button variant="outline" className="flex-1">
                    <MessageSquare className="mr-2 h-4 w-4" />
                    评论
                  </Button>
                  <Button variant="outline" className="flex-1">
                    <Share className="mr-2 h-4 w-4" />
                    分享
                  </Button>
                  <Button variant="outline">
                    <Bookmark className="h-4 w-4" />
                  </Button>
                </div>

                {/* 相关信息 */}
                <div className="bg-gray-50 rounded-lg p-4">
                  <h3 className="font-medium text-gray-900 mb-2">帖子信息</h3>
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-gray-500">帖子ID:</span>
                      <span className="ml-2 font-mono">{selectedPost.id}</span>
                    </div>
                    <div>
                      <span className="text-gray-500">类型:</span>
                      <span className="ml-2">{selectedPost.type}</span>
                    </div>
                    <div>
                      <span className="text-gray-500">创建时间:</span>
                      <span className="ml-2">{selectedPost.created_at}</span>
                    </div>
                    <div>
                      <span className="text-gray-500">更新时间:</span>
                      <span className="ml-2">{selectedPost.updated_at || selectedPost.created_at}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
