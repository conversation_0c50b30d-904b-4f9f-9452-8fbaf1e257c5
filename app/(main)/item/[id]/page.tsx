'use client'

import { useParams } from 'next/navigation'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import Link from 'next/link'

export default function ItemDetailPage() {
  const params = useParams()
  const { id } = params

  return (
    <div className="container mx-auto p-4">
      <Card>
        <CardHeader>
          <CardTitle>详情页</CardTitle>
        </CardHeader>
        <CardContent>
          <p>这里是项目的详细信息。</p>
          <p className="mt-4 font-bold text-lg">当前项目 ID 是: {id}</p>
          
          <div className="mt-6">
            <Link href="/dashboard">
              <Button variant="outline">返回仪表盘</Button>
            </Link>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
