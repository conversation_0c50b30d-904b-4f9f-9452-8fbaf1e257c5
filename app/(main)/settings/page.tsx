'use client'

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import {
  Settings,
  User,
  Bell,
  Shield,
  Palette,
  HelpCircle,
  LogOut,
  Save,
  Eye,
  EyeOff,
  Lock,
  Fingerprint,
  Smartphone,
  Database,
  Download,
  Trash2,
  AlertTriangle,
  Navigation,
  CheckCircle,
  Mail,
  MessageCircle,
  Phone,
  Info
} from 'lucide-react'

export default function SettingsPage() {
  const [activeSection, setActiveSection] = useState('profile')
  const [showPassword, setShowPassword] = useState(false)

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">设置</h1>
        <p className="text-gray-600">管理您的账户设置和偏好</p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* 设置导航 */}
        <Card className="lg:col-span-1">
          <CardHeader>
            <CardTitle className="flex items-center">
              <Settings className="mr-2 h-5 w-5" />
              设置导航
            </CardTitle>
            <CardDescription>
              选择要配置的设置项
            </CardDescription>
          </CardHeader>
          <CardContent>
            <nav className="space-y-2">
              {[
                { icon: User, label: '个人信息', id: 'profile' },
                { icon: Navigation, label: '导航设置', id: 'navigation' },
                { icon: Bell, label: '通知设置', id: 'notifications' },
                { icon: Shield, label: '隐私安全', id: 'privacy' },
                { icon: Database, label: '数据管理', id: 'data' },
                { icon: Palette, label: '外观主题', id: 'appearance' },
                { icon: HelpCircle, label: '帮助支持', id: 'help' }
              ].map((item) => {
                const Icon = item.icon
                return (
                  <button
                    key={item.id}
                    onClick={() => setActiveSection(item.id)}
                    className={`w-full flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                      activeSection === item.id
                        ? 'bg-blue-100 text-blue-700'
                        : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                    }`}
                  >
                    <Icon className="mr-3 h-4 w-4" />
                    {item.label}
                  </button>
                )
              })}
            </nav>
          </CardContent>
        </Card>

        {/* 设置内容 */}
        <div className="lg:col-span-2 space-y-6">
          {/* 个人信息 */}
          {activeSection === 'profile' && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <User className="mr-2 h-5 w-5" />
                  个人信息
                </CardTitle>
                <CardDescription>
                  管理您的个人资料和账户信息
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">用户名</label>
                    <Input
                      type="text"
                      defaultValue="坚持的小明"
                      placeholder="输入用户名"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">邮箱</label>
                    <Input
                      type="email"
                      defaultValue="<EMAIL>"
                      placeholder="输入邮箱地址"
                    />
                  </div>
                </div>

                <div className="space-y-4">
                  <h4 className="font-medium text-gray-900 border-b pb-2">密码设置</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">当前密码</label>
                      <div className="relative">
                        <Input
                          type={showPassword ? "text" : "password"}
                          placeholder="输入当前密码"
                        />
                        <button
                          type="button"
                          onClick={() => setShowPassword(!showPassword)}
                          className="absolute right-3 top-1/2 transform -translate-y-1/2"
                        >
                          {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                        </button>
                      </div>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">新密码</label>
                      <Input
                        type="password"
                        placeholder="输入新密码"
                      />
                    </div>
                  </div>
                </div>

                <div className="flex justify-end pt-4">
                  <Button>
                    <Save className="mr-2 h-4 w-4" />
                    保存更改
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {/* 导航设置 */}
          {activeSection === 'navigation' && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Navigation className="mr-2 h-5 w-5" />
                  导航设置
                </CardTitle>
                <CardDescription>
                  自定义您的导航偏好和快捷方式
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-4">
                  <h4 className="font-medium text-gray-900 border-b pb-2">首页显示</h4>
                  <div className="space-y-3">
                    <label className="flex items-center">
                      <input type="checkbox" className="mr-2" defaultChecked />
                      显示今日任务卡片
                    </label>
                    <label className="flex items-center">
                      <input type="checkbox" className="mr-2" defaultChecked />
                      显示邀请好友入口
                    </label>
                    <label className="flex items-center">
                      <input type="checkbox" className="mr-2" />
                      显示健康监测快捷入口
                    </label>
                  </div>
                </div>

                <div className="space-y-4">
                  <h4 className="font-medium text-gray-900 border-b pb-2">侧边栏设置</h4>
                  <div className="space-y-3">
                    <label className="flex items-center">
                      <input type="checkbox" className="mr-2" defaultChecked />
                      自动折叠侧边栏
                    </label>
                    <label className="flex items-center">
                      <input type="checkbox" className="mr-2" />
                      显示图标提示
                    </label>
                  </div>
                </div>

                <div className="flex justify-end pt-4">
                  <Button>
                    <Save className="mr-2 h-4 w-4" />
                    保存设置
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {/* 通知设置 */}
          {activeSection === 'notifications' && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Bell className="mr-2 h-5 w-5" />
                  通知设置
                </CardTitle>
                <CardDescription>
                  管理您的通知偏好和提醒设置
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-4">
                  <h4 className="font-medium text-gray-900 border-b pb-2">推送通知</h4>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <div>
                        <div className="font-medium">每日打卡提醒</div>
                        <div className="text-sm text-gray-500">每天晚上8点提醒您打卡</div>
                      </div>
                      <input type="checkbox" className="toggle" defaultChecked />
                    </div>
                    <div className="flex items-center justify-between">
                      <div>
                        <div className="font-medium">任务完成提醒</div>
                        <div className="text-sm text-gray-500">提醒您完成今日任务</div>
                      </div>
                      <input type="checkbox" className="toggle" defaultChecked />
                    </div>
                    <div className="flex items-center justify-between">
                      <div>
                        <div className="font-medium">里程碑庆祝</div>
                        <div className="text-sm text-gray-500">达成重要里程碑时通知</div>
                      </div>
                      <input type="checkbox" className="toggle" defaultChecked />
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <h4 className="font-medium text-gray-900 border-b pb-2">邮件通知</h4>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <div>
                        <div className="font-medium">周报总结</div>
                        <div className="text-sm text-gray-500">每周发送进度总结邮件</div>
                      </div>
                      <input type="checkbox" className="toggle" />
                    </div>
                    <div className="flex items-center justify-between">
                      <div>
                        <div className="font-medium">社区动态</div>
                        <div className="text-sm text-gray-500">接收社区重要动态通知</div>
                      </div>
                      <input type="checkbox" className="toggle" />
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <h4 className="font-medium text-gray-900 border-b pb-2">通知时间</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">开始时间</label>
                      <Input type="time" defaultValue="08:00" />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">结束时间</label>
                      <Input type="time" defaultValue="22:00" />
                    </div>
                  </div>
                </div>

                <div className="flex justify-end pt-4">
                  <Button>
                    <Save className="mr-2 h-4 w-4" />
                    保存设置
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {/* 隐私安全 */}
          {activeSection === 'privacy' && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Shield className="mr-2 h-5 w-5" />
                  隐私安全
                </CardTitle>
                <CardDescription>
                  管理您的隐私设置和账户安全
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-4">
                  <h4 className="font-medium text-gray-900 border-b pb-2">账户安全</h4>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center">
                        <Lock className="h-5 w-5 text-gray-400 mr-3" />
                        <div>
                          <div className="font-medium">修改密码</div>
                          <div className="text-sm text-gray-500">定期更新密码保护账户安全</div>
                        </div>
                      </div>
                      <Button variant="outline" size="sm">修改</Button>
                    </div>
                    <div className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center">
                        <Fingerprint className="h-5 w-5 text-gray-400 mr-3" />
                        <div>
                          <div className="font-medium">两步验证</div>
                          <div className="text-sm text-gray-500">启用两步验证增强安全性</div>
                        </div>
                      </div>
                      <Button variant="outline" size="sm">启用</Button>
                    </div>
                    <div className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center">
                        <Smartphone className="h-5 w-5 text-gray-400 mr-3" />
                        <div>
                          <div className="font-medium">设备管理</div>
                          <div className="text-sm text-gray-500">查看和管理登录设备</div>
                        </div>
                      </div>
                      <Button variant="outline" size="sm">管理</Button>
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <h4 className="font-medium text-gray-900 border-b pb-2">隐私设置</h4>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <div>
                        <div className="font-medium">个人资料可见性</div>
                        <div className="text-sm text-gray-500">控制其他用户是否能看到您的资料</div>
                      </div>
                      <select className="border rounded px-3 py-1">
                        <option>公开</option>
                        <option>仅好友</option>
                        <option>私密</option>
                      </select>
                    </div>
                    <div className="flex items-center justify-between">
                      <div>
                        <div className="font-medium">进度分享</div>
                        <div className="text-sm text-gray-500">允许分享您的戒断进度</div>
                      </div>
                      <input type="checkbox" className="toggle" defaultChecked />
                    </div>
                    <div className="flex items-center justify-between">
                      <div>
                        <div className="font-medium">数据分析</div>
                        <div className="text-sm text-gray-500">允许使用匿名数据改进服务</div>
                      </div>
                      <input type="checkbox" className="toggle" defaultChecked />
                    </div>
                  </div>
                </div>

                <div className="flex justify-end pt-4">
                  <Button>
                    <Save className="mr-2 h-4 w-4" />
                    保存设置
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {/* 数据管理 */}
          {activeSection === 'data' && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Database className="mr-2 h-5 w-5" />
                  数据管理
                </CardTitle>
                <CardDescription>
                  管理您的数据备份、导出和删除
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-4">
                  <h4 className="font-medium text-gray-900 border-b pb-2">数据备份</h4>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center">
                        <Download className="h-5 w-5 text-blue-500 mr-3" />
                        <div>
                          <div className="font-medium">导出个人数据</div>
                          <div className="text-sm text-gray-500">下载您的所有个人数据副本</div>
                        </div>
                      </div>
                      <Button variant="outline" size="sm">
                        <Download className="h-4 w-4 mr-1" />
                        导出
                      </Button>
                    </div>
                    <div className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center">
                        <Database className="h-5 w-5 text-green-500 mr-3" />
                        <div>
                          <div className="font-medium">自动备份</div>
                          <div className="text-sm text-gray-500">定期自动备份您的数据</div>
                        </div>
                      </div>
                      <input type="checkbox" className="toggle" defaultChecked />
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <h4 className="font-medium text-gray-900 border-b pb-2">数据清理</h4>
                  <div className="space-y-3">
                    <div className="p-3 border rounded-lg bg-yellow-50 border-yellow-200">
                      <div className="flex items-center mb-2">
                        <AlertTriangle className="h-5 w-5 text-yellow-600 mr-2" />
                        <div className="font-medium text-yellow-800">清理缓存数据</div>
                      </div>
                      <div className="text-sm text-yellow-700 mb-3">
                        清理应用缓存可以释放存储空间，但可能需要重新加载某些内容
                      </div>
                      <Button variant="outline" size="sm" className="border-yellow-300 text-yellow-700">
                        清理缓存
                      </Button>
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <h4 className="font-medium text-gray-900 border-b pb-2">危险操作</h4>
                  <div className="space-y-3">
                    <div className="p-3 border rounded-lg bg-red-50 border-red-200">
                      <div className="flex items-center mb-2">
                        <Trash2 className="h-5 w-5 text-red-600 mr-2" />
                        <div className="font-medium text-red-800">删除账户</div>
                      </div>
                      <div className="text-sm text-red-700 mb-3">
                        永久删除您的账户和所有相关数据。此操作不可撤销！
                      </div>
                      <Button variant="outline" size="sm" className="border-red-300 text-red-700">
                        删除账户
                      </Button>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* 外观主题 */}
          {activeSection === 'appearance' && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Palette className="mr-2 h-5 w-5" />
                  外观主题
                </CardTitle>
                <CardDescription>
                  自定义应用的外观和主题设置
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-4">
                  <h4 className="font-medium text-gray-900 border-b pb-2">主题模式</h4>
                  <div className="grid grid-cols-3 gap-3">
                    <div className="p-3 border rounded-lg cursor-pointer hover:bg-gray-50 border-blue-500 bg-blue-50">
                      <div className="w-full h-16 bg-white rounded mb-2 border"></div>
                      <div className="text-center text-sm font-medium">浅色模式</div>
                    </div>
                    <div className="p-3 border rounded-lg cursor-pointer hover:bg-gray-50">
                      <div className="w-full h-16 bg-gray-800 rounded mb-2"></div>
                      <div className="text-center text-sm font-medium">深色模式</div>
                    </div>
                    <div className="p-3 border rounded-lg cursor-pointer hover:bg-gray-50">
                      <div className="w-full h-16 bg-gradient-to-r from-white to-gray-800 rounded mb-2"></div>
                      <div className="text-center text-sm font-medium">跟随系统</div>
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <h4 className="font-medium text-gray-900 border-b pb-2">主题色彩</h4>
                  <div className="grid grid-cols-6 gap-3">
                    {['bg-blue-500', 'bg-green-500', 'bg-purple-500', 'bg-pink-500', 'bg-orange-500', 'bg-teal-500'].map((color, index) => (
                      <div key={index} className={`w-12 h-12 ${color} rounded-lg cursor-pointer border-2 ${index === 0 ? 'border-gray-400' : 'border-transparent'} hover:scale-110 transition-transform`}></div>
                    ))}
                  </div>
                </div>

                <div className="space-y-4">
                  <h4 className="font-medium text-gray-900 border-b pb-2">显示设置</h4>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <div>
                        <div className="font-medium">紧凑模式</div>
                        <div className="text-sm text-gray-500">减少界面元素间距</div>
                      </div>
                      <input type="checkbox" className="toggle" />
                    </div>
                    <div className="flex items-center justify-between">
                      <div>
                        <div className="font-medium">动画效果</div>
                        <div className="text-sm text-gray-500">启用界面过渡动画</div>
                      </div>
                      <input type="checkbox" className="toggle" defaultChecked />
                    </div>
                  </div>
                </div>

                <div className="flex justify-end pt-4">
                  <Button>
                    <Save className="mr-2 h-4 w-4" />
                    保存设置
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {/* 帮助支持 */}
          {activeSection === 'help' && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <HelpCircle className="mr-2 h-5 w-5" />
                  帮助支持
                </CardTitle>
                <CardDescription>
                  获取帮助、反馈问题和了解更多信息
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-4">
                  <h4 className="font-medium text-gray-900 border-b pb-2">帮助资源</h4>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50 cursor-pointer">
                      <div className="flex items-center">
                        <Info className="h-5 w-5 text-blue-500 mr-3" />
                        <div>
                          <div className="font-medium">使用指南</div>
                          <div className="text-sm text-gray-500">了解如何使用各项功能</div>
                        </div>
                      </div>
                      <Button variant="ghost" size="sm">查看</Button>
                    </div>
                    <div className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50 cursor-pointer">
                      <div className="flex items-center">
                        <MessageCircle className="h-5 w-5 text-green-500 mr-3" />
                        <div>
                          <div className="font-medium">常见问题</div>
                          <div className="text-sm text-gray-500">查看常见问题解答</div>
                        </div>
                      </div>
                      <Button variant="ghost" size="sm">查看</Button>
                    </div>
                    <div className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50 cursor-pointer">
                      <div className="flex items-center">
                        <Phone className="h-5 w-5 text-purple-500 mr-3" />
                        <div>
                          <div className="font-medium">联系客服</div>
                          <div className="text-sm text-gray-500">获得专业的人工支持</div>
                        </div>
                      </div>
                      <Button variant="ghost" size="sm">联系</Button>
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <h4 className="font-medium text-gray-900 border-b pb-2">反馈建议</h4>
                  <div className="space-y-3">
                    <div className="p-3 border rounded-lg">
                      <div className="font-medium mb-2">意见反馈</div>
                      <textarea
                        className="w-full p-2 border rounded resize-none"
                        rows={3}
                        placeholder="请告诉我们您的建议或遇到的问题..."
                      ></textarea>
                      <div className="flex justify-end mt-2">
                        <Button size="sm">提交反馈</Button>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <h4 className="font-medium text-gray-900 border-b pb-2">应用信息</h4>
                  <div className="space-y-2 text-sm text-gray-600">
                    <div className="flex justify-between">
                      <span>应用版本</span>
                      <span>v1.0.0</span>
                    </div>
                    <div className="flex justify-between">
                      <span>最后更新</span>
                      <span>2024-01-20</span>
                    </div>
                    <div className="flex justify-between">
                      <span>用户协议</span>
                      <Button variant="link" size="sm" className="p-0 h-auto">查看</Button>
                    </div>
                    <div className="flex justify-between">
                      <span>隐私政策</span>
                      <Button variant="link" size="sm" className="p-0 h-auto">查看</Button>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  )
}
