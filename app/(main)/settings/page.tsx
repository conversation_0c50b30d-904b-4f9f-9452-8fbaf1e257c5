'use client'

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import {
  Settings,
  User,
  Bell,
  Shield,
  Palette,
  HelpCircle,
  LogOut,
  Save,
  Eye,
  EyeOff,
  Lock,
  Fingerprint,
  Smartphone,
  Database,
  Download,
  Trash2,
  AlertTriangle,
  Navigation,
  CheckCircle,
  Mail,
  MessageCircle,
  Phone,
  Info
} from 'lucide-react'

export default function SettingsPage() {
  const [activeSection, setActiveSection] = useState('profile')
  const [showPassword, setShowPassword] = useState(false)

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">设置</h1>
        <p className="text-gray-600">管理您的账户设置和偏好</p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* 设置导航 */}
        <Card className="lg:col-span-1">
          <CardHeader>
            <CardTitle className="flex items-center">
              <Settings className="mr-2 h-5 w-5" />
              设置导航
            </CardTitle>
            <CardDescription>
              选择要配置的设置项
            </CardDescription>
          </CardHeader>
          <CardContent>
            <nav className="space-y-2">
              {[
                { icon: User, label: '个人信息', id: 'profile' },
                { icon: Navigation, label: '导航设置', id: 'navigation' },
                { icon: Bell, label: '通知设置', id: 'notifications' },
                { icon: Shield, label: '隐私安全', id: 'privacy' },
                { icon: Database, label: '数据管理', id: 'data' },
                { icon: Palette, label: '外观主题', id: 'appearance' },
                { icon: HelpCircle, label: '帮助支持', id: 'help' }
              ].map((item) => {
                const Icon = item.icon
                return (
                  <button
                    key={item.id}
                    onClick={() => setActiveSection(item.id)}
                    className={`w-full flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                      activeSection === item.id
                        ? 'bg-blue-100 text-blue-700'
                        : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                    }`}
                  >
                    <Icon className="mr-3 h-4 w-4" />
                    {item.label}
                  </button>
                )
              })}
            </nav>
          </CardContent>
        </Card>

        {/* 设置内容 */}
        <div className="lg:col-span-2 space-y-6">
          {/* 个人信息 */}
          {activeSection === 'profile' && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <User className="mr-2 h-5 w-5" />
                  个人信息
                </CardTitle>
                <CardDescription>
                  管理您的个人资料和账户信息
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">用户名</label>
                    <Input
                      type="text"
                      defaultValue="坚持的小明"
                      placeholder="输入用户名"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">邮箱</label>
                    <Input
                      type="email"
                      defaultValue="<EMAIL>"
                      placeholder="输入邮箱地址"
                    />
                  </div>
                </div>

                <div className="space-y-4">
                  <h4 className="font-medium text-gray-900 border-b pb-2">密码设置</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">当前密码</label>
                      <div className="relative">
                        <Input
                          type={showPassword ? "text" : "password"}
                          placeholder="输入当前密码"
                        />
                        <button
                          type="button"
                          onClick={() => setShowPassword(!showPassword)}
                          className="absolute right-3 top-1/2 transform -translate-y-1/2"
                        >
                          {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                        </button>
                      </div>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">新密码</label>
                      <Input
                        type="password"
                        placeholder="输入新密码"
                      />
                    </div>
                  </div>
                </div>

                <div className="flex justify-end pt-4">
                  <Button>
                    <Save className="mr-2 h-4 w-4" />
                    保存更改
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {/* 其他设置项可以在这里添加 */}
          {activeSection !== 'profile' && (
            <Card>
              <CardContent className="text-center py-12">
                <div className="text-gray-500">
                  {activeSection} 设置功能正在开发中...
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  )
}
