'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import AddBlockItemDialog from '@/components/tools/add-block-item-dialog'
import AddContactDialog from '@/components/tools/add-contact-dialog'
import { 
  Shield,
  Zap,
  Phone,
  Plus,
  Trash2,
  Play,
  Pause,
  Volume2,
  VolumeX,
  Heart,
  Wind,
  Brain,
  Clock,
  Settings,
  AlertTriangle
} from 'lucide-react'
import { mockApiResponse } from '@/lib/mock-data'

interface BlockedItem {
  id: string
  type: 'website' | 'app' | 'keyword'
  name: string
  url?: string
  category?: string
  description?: string
  severity: 'low' | 'medium' | 'high'
  isActive: boolean
}

interface EmergencyContact {
  id: string
  name: string
  phone: string
  relationship: string
  description?: string
  priority: 'high' | 'medium' | 'low'
  isAvailable24h: boolean
}

export default function ToolsPage() {
  const [blockedItems, setBlockedItems] = useState<BlockedItem[]>([])
  const [emergencyContacts, setEmergencyContacts] = useState<EmergencyContact[]>([])
  const [newBlockItem, setNewBlockItem] = useState('')
  const [newContactName, setNewContactName] = useState('')
  const [newContactPhone, setNewContactPhone] = useState('')
  const [newContactRelationship, setNewContactRelationship] = useState('')
  const [isBreathingActive, setIsBreathingActive] = useState(false)
  const [isMeditationActive, setIsMeditationActive] = useState(false)
  const [breathingTimer, setBreathingTimer] = useState(0)
  const [meditationTimer, setMeditationTimer] = useState(0)
  const [loading, setLoading] = useState(true)
  const [deleteConfirm, setDeleteConfirm] = useState<{
    show: boolean
    type: 'blocked' | 'contact'
    id: string
    name: string
  }>({ show: false, type: 'blocked', id: '', name: '' })

  useEffect(() => {
    loadToolsData()
  }, [])

  useEffect(() => {
    let interval: NodeJS.Timeout
    if (isBreathingActive && breathingTimer > 0) {
      interval = setInterval(() => {
        setBreathingTimer(prev => {
          if (prev <= 1) {
            setIsBreathingActive(false)
            return 0
          }
          return prev - 1
        })
      }, 1000)
    }
    return () => clearInterval(interval)
  }, [isBreathingActive, breathingTimer])

  useEffect(() => {
    let interval: NodeJS.Timeout
    if (isMeditationActive && meditationTimer > 0) {
      interval = setInterval(() => {
        setMeditationTimer(prev => {
          if (prev <= 1) {
            setIsMeditationActive(false)
            return 0
          }
          return prev - 1
        })
      }, 1000)
    }
    return () => clearInterval(interval)
  }, [isMeditationActive, meditationTimer])

  const loadToolsData = async () => {
    try {
      const mockBlockedItems: BlockedItem[] = [
        {
          id: '1',
          type: 'website',
          name: 'Instagram',
          url: 'instagram.com',
          category: 'social',
          severity: 'medium',
          isActive: true
        },
        {
          id: '2',
          type: 'app',
          name: '抖音',
          category: 'video',
          severity: 'high',
          isActive: true
        },
        {
          id: '3',
          type: 'keyword',
          name: '敏感内容',
          category: 'adult',
          severity: 'high',
          isActive: true
        }
      ]

      const mockContacts: EmergencyContact[] = [
        {
          id: '1',
          name: '小明',
          phone: '138****1234',
          relationship: 'friend',
          priority: 'medium',
          isAvailable24h: false
        },
        {
          id: '2',
          name: '心理咨询师',
          phone: '400****5678',
          relationship: 'professional',
          priority: 'high',
          isAvailable24h: true
        }
      ]

      const [blockedResponse, contactsResponse] = await Promise.all([
        mockApiResponse(mockBlockedItems),
        mockApiResponse(mockContacts)
      ])

      setBlockedItems(blockedResponse.data)
      setEmergencyContacts(contactsResponse.data)
    } catch (error) {
      console.error('加载工具数据失败:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleAddBlockItem = async (item: Omit<BlockedItem, 'id'>) => {
    try {
      const newItem: BlockedItem = {
        ...item,
        id: Date.now().toString()
      }

      await mockApiResponse({ success: true })
      setBlockedItems(prev => [...prev, newItem])
    } catch (error) {
      console.error('添加屏蔽项失败:', error)
    }
  }

  const showDeleteConfirm = (type: 'blocked' | 'contact', id: string, name: string) => {
    setDeleteConfirm({ show: true, type, id, name })
  }

  const confirmDelete = async () => {
    try {
      await mockApiResponse({ success: true })
      if (deleteConfirm.type === 'blocked') {
        setBlockedItems(prev => prev.filter(item => item.id !== deleteConfirm.id))
      } else {
        setEmergencyContacts(prev => prev.filter(contact => contact.id !== deleteConfirm.id))
      }
      setDeleteConfirm({ show: false, type: 'blocked', id: '', name: '' })
    } catch (error) {
      console.error('删除失败:', error)
    }
  }

  const cancelDelete = () => {
    setDeleteConfirm({ show: false, type: 'blocked', id: '', name: '' })
  }

  const handleRemoveBlockItem = async (id: string) => {
    const item = blockedItems.find(item => item.id === id)
    if (item) {
      showDeleteConfirm('blocked', id, item.name)
    }
  }

  const handleToggleBlockItem = async (id: string) => {
    try {
      await mockApiResponse({ success: true })
      setBlockedItems(prev => 
        prev.map(item => 
          item.id === id ? { ...item, isActive: !item.isActive } : item
        )
      )
    } catch (error) {
      console.error('切换屏蔽状态失败:', error)
    }
  }

  const handleAddContact = async (contact: Omit<EmergencyContact, 'id'>) => {
    try {
      const newContact: EmergencyContact = {
        ...contact,
        id: Date.now().toString()
      }

      await mockApiResponse({ success: true })
      setEmergencyContacts(prev => [...prev, newContact])
    } catch (error) {
      console.error('添加联系人失败:', error)
    }
  }

  const handleRemoveContact = async (id: string) => {
    const contact = emergencyContacts.find(contact => contact.id === id)
    if (contact) {
      showDeleteConfirm('contact', id, contact.name)
    }
  }

  const startBreathing = (minutes: number) => {
    setBreathingTimer(minutes * 60)
    setIsBreathingActive(true)
  }

  const startMeditation = (minutes: number) => {
    setMeditationTimer(minutes * 60)
    setIsMeditationActive(true)
  }

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  }

  const handleEmergencyCall = async (contact: EmergencyContact) => {
    try {
      await mockApiResponse({ success: true })
      // 在实际应用中，这里会触发电话拨打或发送紧急消息
      alert(`正在联系 ${contact.name} (${contact.phone})`)
    } catch (error) {
      console.error('紧急联系失败:', error)
    }
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-6"></div>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className="h-96 bg-gray-200 rounded-lg"></div>
            <div className="h-96 bg-gray-200 rounded-lg"></div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">防诱惑工具</h1>
        <p className="text-gray-600">使用科学的方法帮助您抵御诱惑，保持专注</p>
      </div>

      {/* 快速工具栏 */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <Button
          variant={isBreathingActive ? "destructive" : "gradient"}
          className="h-20 flex-col"
          onClick={() => isBreathingActive ? setIsBreathingActive(false) : startBreathing(5)}
        >
          <Wind className="h-6 w-6 mb-1" />
          <span className="text-sm">
            {isBreathingActive ? `${formatTime(breathingTimer)}` : '深呼吸'}
          </span>
        </Button>

        <Button
          variant={isMeditationActive ? "destructive" : "gradient"}
          className="h-20 flex-col"
          onClick={() => isMeditationActive ? setIsMeditationActive(false) : startMeditation(10)}
        >
          <Brain className="h-6 w-6 mb-1" />
          <span className="text-sm">
            {isMeditationActive ? `${formatTime(meditationTimer)}` : '冥想'}
          </span>
        </Button>

        <Button
          variant="outline"
          className="h-20 flex-col"
          onClick={() => alert('正在播放舒缓音乐...')}
        >
          <Volume2 className="h-6 w-6 mb-1" />
          <span className="text-sm">舒缓音乐</span>
        </Button>

        <Button
          variant="outline"
          className="h-20 flex-col text-red-600 border-red-200 hover:bg-red-50"
          onClick={() => {
            if (emergencyContacts.length > 0) {
              handleEmergencyCall(emergencyContacts[0])
            } else {
              alert('请先添加紧急联系人')
            }
          }}
        >
          <AlertTriangle className="h-6 w-6 mb-1" />
          <span className="text-sm">紧急求助</span>
        </Button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 内容屏蔽 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Shield className="mr-2 h-5 w-5" />
              内容屏蔽
            </CardTitle>
            <CardDescription>
              屏蔽可能触发不良行为的网站和应用
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {/* 添加新屏蔽项 */}
              <div className="flex justify-between items-center">
                <div className="text-sm text-gray-600">
                  管理屏蔽的网站、应用和关键词
                </div>
                <AddBlockItemDialog onAdd={handleAddBlockItem} />
              </div>

              {/* 屏蔽列表 */}
              <div className="space-y-2">
                {blockedItems.map((item) => (
                  <div
                    key={item.id}
                    className={`flex items-center justify-between p-3 rounded-lg border ${
                      item.isActive ? 'bg-red-50 border-red-200' : 'bg-gray-50 border-gray-200'
                    }`}
                  >
                    <div className="flex items-center gap-3">
                      <div className={`w-3 h-3 rounded-full ${
                        item.isActive ? 'bg-red-500' : 'bg-gray-400'
                      }`} />
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-1">
                          <p className="font-medium text-sm">{item.name}</p>
                          {item.severity && (
                            <span className={`px-1.5 py-0.5 text-xs rounded ${
                              item.severity === 'high' ? 'bg-red-100 text-red-700' :
                              item.severity === 'medium' ? 'bg-yellow-100 text-yellow-700' :
                              'bg-green-100 text-green-700'
                            }`}>
                              {item.severity === 'high' ? '高' : item.severity === 'medium' ? '中' : '低'}
                            </span>
                          )}
                        </div>
                        <p className="text-xs text-gray-500">
                          {item.type === 'website' ? '网站' :
                           item.type === 'app' ? '应用' : '关键词'}
                          {item.url && ` • ${item.url}`}
                          {item.category && ` • ${item.category}`}
                        </p>
                        {item.description && (
                          <p className="text-xs text-gray-400 mt-1">{item.description}</p>
                        )}
                      </div>
                    </div>
                    <div className="flex gap-1">
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => handleToggleBlockItem(item.id)}
                      >
                        {item.isActive ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
                      </Button>
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => handleRemoveBlockItem(item.id)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                ))}

                {blockedItems.length === 0 && (
                  <div className="text-center py-8 text-gray-500">
                    <Shield className="mx-auto h-12 w-12 mb-2" />
                    <p>暂无屏蔽项</p>
                  </div>
                )}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 紧急联系人 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Phone className="mr-2 h-5 w-5" />
              紧急联系人
            </CardTitle>
            <CardDescription>
              在需要帮助时快速联系信任的人
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {/* 添加新联系人 */}
              <div className="space-y-2">
                <Input
                  placeholder="姓名"
                  value={newContactName}
                  onChange={(e) => setNewContactName(e.target.value)}
                />
                <Input
                  placeholder="电话号码"
                  value={newContactPhone}
                  onChange={(e) => setNewContactPhone(e.target.value)}
                />
                <Input
                  placeholder="关系 (如: 朋友、家人)"
                  value={newContactRelationship}
                  onChange={(e) => setNewContactRelationship(e.target.value)}
                />
                <Button onClick={handleAddContact} className="w-full">
                  <Plus className="mr-2 h-4 w-4" />
                  添加联系人
                </Button>
              </div>

              {/* 联系人列表 */}
              <div className="space-y-2">
                {emergencyContacts.map((contact) => (
                  <div
                    key={contact.id}
                    className="flex items-center justify-between p-3 rounded-lg border border-gray-200 hover:border-gray-300"
                  >
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                        <span className="text-blue-600 font-medium text-sm">
                          {contact.name[0]}
                        </span>
                      </div>
                      <div>
                        <p className="font-medium text-sm">{contact.name}</p>
                        <p className="text-xs text-gray-500">
                          {contact.relationship} • {contact.phone}
                        </p>
                      </div>
                    </div>
                    <div className="flex gap-1">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleEmergencyCall(contact)}
                      >
                        <Phone className="h-4 w-4" />
                      </Button>
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => handleRemoveContact(contact.id)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                ))}

                {emergencyContacts.length === 0 && (
                  <div className="text-center py-8 text-gray-500">
                    <Phone className="mx-auto h-12 w-12 mb-2" />
                    <p>暂无紧急联系人</p>
                  </div>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 冲动疏导指南 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Heart className="mr-2 h-5 w-5" />
            冲动疏导指南
          </CardTitle>
          <CardDescription>
            当感到冲动时，按照以下步骤进行自我调节
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="p-4 rounded-lg bg-blue-50 border border-blue-200">
              <div className="flex items-center mb-2">
                <div className="w-6 h-6 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-bold mr-2">
                  1
                </div>
                <h4 className="font-medium text-blue-900">立即停止</h4>
              </div>
              <p className="text-sm text-blue-700">
                停止当前行为，离开触发环境，转移注意力到其他地方
              </p>
            </div>

            <div className="p-4 rounded-lg bg-green-50 border border-green-200">
              <div className="flex items-center mb-2">
                <div className="w-6 h-6 bg-green-500 text-white rounded-full flex items-center justify-center text-sm font-bold mr-2">
                  2
                </div>
                <h4 className="font-medium text-green-900">深呼吸调节</h4>
              </div>
              <p className="text-sm text-green-700">
                进行4-7-8呼吸法：吸气4秒，憋气7秒，呼气8秒，重复3-5次
              </p>
            </div>

            <div className="p-4 rounded-lg bg-purple-50 border border-purple-200">
              <div className="flex items-center mb-2">
                <div className="w-6 h-6 bg-purple-500 text-white rounded-full flex items-center justify-center text-sm font-bold mr-2">
                  3
                </div>
                <h4 className="font-medium text-purple-900">寻求支持</h4>
              </div>
              <p className="text-sm text-purple-700">
                联系信任的朋友或使用社区功能，获得鼓励和支持
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 删除确认对话框 */}
      {deleteConfirm.show && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-sm mx-4">
            <div className="flex items-center mb-4">
              <AlertTriangle className="h-6 w-6 text-red-500 mr-3" />
              <h3 className="text-lg font-semibold text-gray-900">确认删除</h3>
            </div>
            <p className="text-gray-600 mb-6">
                            您确定要删除 &ldquo;{deleteConfirm.name}&rdquo; 吗？此操作无法撤销。
            </p>
            <div className="flex gap-3">
              <button
                onClick={cancelDelete}
                className="flex-1 px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors"
              >
                取消
              </button>
              <button
                onClick={confirmDelete}
                className="flex-1 px-4 py-2 text-white bg-red-500 rounded-md hover:bg-red-600 transition-colors"
              >
                确认删除
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
