'use client'

import { useState, useEffect } from 'react'
import { useSearchParams } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
import {
  User,
  Calendar,
  Award,
  Target,
  TrendingUp,
  Edit,
  Settings,
  Trophy,
  Flame,
  Star,
  Gift,
  Users,
  Heart,
  Bookmark,
  Share,
  MessageCircle,
  Eye,
  ThumbsUp,
  Coffee,
  Badge as BadgeIcon,
  ChevronRight
} from 'lucide-react'
import { mockUser, generateMockUserStats, mockMilestones, mockApiResponse } from '@/lib/mock-data'
import { formatDate, formatRelativeDate } from '@/lib/utils'
import type { User as UserType, UserStats, Milestone } from '@/types'
import InviteCodeManager from '@/components/profile/invite-code-manager'
import SocialDataList from '@/components/profile/social-data-list'

export default function ProfilePage() {
  const [user, setUser] = useState<UserType | null>(null)
  const [stats, setStats] = useState<UserStats | null>(null)
  const [milestones, setMilestones] = useState<Milestone[]>([])
  const [loading, setLoading] = useState(true)
  const [activeTab, setActiveTab] = useState<'overview' | 'achievements' | 'invite' | 'social'>('overview')
  const [socialDataView, setSocialDataView] = useState<'overview' | 'likes' | 'bookmarks' | 'shares' | 'comments'>('overview')
  const [socialStats, setSocialStats] = useState({
    totalLikes: 0,
    totalBookmarks: 0,
    totalShares: 0,
    totalComments: 0,
    totalViews: 0,
    receivedLikes: 0,
    givenLikes: 0,
    postsCount: 0,
    followersCount: 0,
    followingCount: 0
  })
  const [userBadge, setUserBadge] = useState({
    id: 'USER001',
    level: 'VIP',
    joinDate: '2024-01-15',
    lastActive: '2025-01-18'
  })
  const searchParams = useSearchParams()

  useEffect(() => {
    loadProfileData()

    // 从URL参数获取标签页
    const tabFromUrl = searchParams.get('tab')
    if (tabFromUrl === 'invite' || tabFromUrl === 'achievements') {
      setActiveTab(tabFromUrl as 'achievements' | 'invite')
    }
  }, [searchParams])

  const loadProfileData = async () => {
    try {
      const [userResponse, statsResponse, milestonesResponse] = await Promise.all([
        mockApiResponse(mockUser),
        mockApiResponse(generateMockUserStats()),
        mockApiResponse(mockMilestones)
      ])

      setUser(userResponse.data)
      setStats(statsResponse.data)
      setMilestones(milestonesResponse.data)

      // 模拟社交数据
      setSocialStats({
        totalLikes: Math.floor(Math.random() * 500) + 100,
        totalBookmarks: Math.floor(Math.random() * 200) + 50,
        totalShares: Math.floor(Math.random() * 150) + 30,
        totalComments: Math.floor(Math.random() * 300) + 80,
        totalViews: Math.floor(Math.random() * 2000) + 500,
        receivedLikes: Math.floor(Math.random() * 800) + 200,
        givenLikes: Math.floor(Math.random() * 600) + 150,
        postsCount: Math.floor(Math.random() * 50) + 10,
        followersCount: Math.floor(Math.random() * 100) + 20,
        followingCount: Math.floor(Math.random() * 80) + 15
      })
    } catch (error) {
      console.error('加载个人资料失败:', error)
    } finally {
      setLoading(false)
    }
  }

  const getGoalLabel = (goal?: string) => {
    switch (goal) {
      case 'quit_porn': return '戒除色情'
      case 'reduce_frequency': return '减少频率'
      case 'self_improvement': return '自我提升'
      default: return '未设置'
    }
  }

  const getHistoryLabel = (history?: string) => {
    switch (history) {
      case 'beginner': return '新手'
      case 'tried_failed': return '尝试过但失败'
      case 'experienced': return '有一定经验'
      default: return '未设置'
    }
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-6"></div>
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div className="h-96 bg-gray-200 rounded-lg"></div>
            <div className="lg:col-span-2 h-96 bg-gray-200 rounded-lg"></div>
          </div>
        </div>
      </div>
    )
  }

  const achievedMilestones = milestones.filter(m => m.achieved)
  const nextMilestone = milestones.find(m => !m.achieved)

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">个人中心</h1>
          <p className="text-gray-600">管理您的个人信息和成就</p>
        </div>
        <Button variant="outline">
          <Edit className="mr-2 h-4 w-4" />
          编辑资料
        </Button>
      </div>

      {/* 标签页导航 */}
      <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg">
        <button
          onClick={() => setActiveTab('overview')}
          className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
            activeTab === 'overview'
              ? 'bg-white text-blue-600 shadow-sm'
              : 'text-gray-600 hover:text-gray-900'
          }`}
        >
          <User className="mr-2 h-4 w-4 inline" />
          个人概览
        </button>
        <button
          onClick={() => setActiveTab('achievements')}
          className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
            activeTab === 'achievements'
              ? 'bg-white text-blue-600 shadow-sm'
              : 'text-gray-600 hover:text-gray-900'
          }`}
        >
          <Trophy className="mr-2 h-4 w-4 inline" />
          成就里程碑
        </button>
        <button
          onClick={() => setActiveTab('invite')}
          className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
            activeTab === 'invite'
              ? 'bg-white text-blue-600 shadow-sm'
              : 'text-gray-600 hover:text-gray-900'
          }`}
        >
          <Gift className="mr-2 h-4 w-4 inline" />
          邀请好友
        </button>
        <button
          onClick={() => setActiveTab('social')}
          className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
            activeTab === 'social'
              ? 'bg-white text-blue-600 shadow-sm'
              : 'text-gray-600 hover:text-gray-900'
          }`}
        >
          <Heart className="mr-2 h-4 w-4 inline" />
          社交数据
        </button>
      </div>

      {/* 标签页内容 */}
      {activeTab === 'overview' && (
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* 个人信息卡片 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <User className="mr-2 h-5 w-5" />
              个人信息
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="text-center">
              <div className="w-20 h-20 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white text-2xl font-bold mx-auto mb-4">
                {user?.username?.[0] || user?.email[0].toUpperCase()}
              </div>
              <h3 className="text-lg font-semibold">{user?.username || '用户'}</h3>
              <p className="text-gray-600 text-sm">{user?.email}</p>
            </div>

            <div className="space-y-3 pt-4 border-t">
              <div className="flex justify-between">
                <span className="text-gray-600">年龄</span>
                <span className="font-medium">{user?.profile?.age || '未设置'}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">目标</span>
                <span className="font-medium">{getGoalLabel(user?.profile?.goal)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">经验</span>
                <span className="font-medium">{getHistoryLabel(user?.profile?.history)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">加入时间</span>
                <span className="font-medium">{formatDate(user?.created_at || '')}</span>
              </div>
            </div>

            <div className="pt-4 border-t">
              <div className="flex justify-between items-center mb-2">
                <span className="text-gray-600">正气值</span>
                <span className="font-bold text-yellow-600">
                  {user?.profile?.positive_energy_score || 0}
                </span>
              </div>
              <Progress value={user?.profile?.positive_energy_score || 0} className="h-2" />
            </div>
          </CardContent>
        </Card>

        {/* 统计数据和成就 */}
        <div className="lg:col-span-2 space-y-6">
          {/* 核心统计 */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">连续天数</CardTitle>
                <Flame className="h-4 w-4 text-orange-500" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats?.current_streak || 0}</div>
                <p className="text-xs text-muted-foreground">
                  最高: {stats?.longest_streak || 0} 天
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">总天数</CardTitle>
                <Calendar className="h-4 w-4 text-blue-500" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats?.total_days || 0}</div>
                <p className="text-xs text-muted-foreground">
                  成功率: {stats?.success_rate || 0}%
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">成就数量</CardTitle>
                <Trophy className="h-4 w-4 text-yellow-500" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{achievedMilestones.length}</div>
                <p className="text-xs text-muted-foreground">
                  共 {milestones.length} 个成就
                </p>
              </CardContent>
            </Card>
          </div>

          {/* 成就系统 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Award className="mr-2 h-5 w-5" />
                成就系统
              </CardTitle>
              <CardDescription>
                您的里程碑和成就记录
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {milestones.map((milestone) => (
                  <div
                    key={milestone.id}
                    className={`flex items-center p-4 rounded-lg border ${
                      milestone.achieved 
                        ? 'bg-green-50 border-green-200' 
                        : 'bg-gray-50 border-gray-200'
                    }`}
                  >
                    <div className={`w-12 h-12 rounded-full flex items-center justify-center mr-4 ${
                      milestone.achieved 
                        ? 'bg-green-500 text-white' 
                        : 'bg-gray-300 text-gray-600'
                    }`}>
                      {milestone.achieved ? (
                        <Trophy className="h-6 w-6" />
                      ) : (
                        <Target className="h-6 w-6" />
                      )}
                    </div>
                    <div className="flex-1">
                      <h4 className={`font-semibold ${
                        milestone.achieved ? 'text-green-700' : 'text-gray-700'
                      }`}>
                        {milestone.title}
                      </h4>
                      <p className="text-sm text-gray-600 mt-1">
                        {milestone.description}
                      </p>
                      <div className="flex items-center mt-2 text-xs text-gray-500">
                        <Target className="mr-1 h-3 w-3" />
                        目标: {milestone.target_days} 天
                        {milestone.achieved && milestone.achieved_at && (
                          <>
                            <span className="mx-2">•</span>
                            <Star className="mr-1 h-3 w-3" />
                            达成于 {formatDate(milestone.achieved_at)}
                          </>
                        )}
                      </div>
                    </div>
                    {milestone.achieved && (
                      <div className="text-green-500">
                        <Award className="h-6 w-6" />
                      </div>
                    )}
                  </div>
                ))}
              </div>

              {nextMilestone && (
                <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                  <h4 className="font-semibold text-blue-700 mb-2">下一个目标</h4>
                  <p className="text-blue-600 text-sm mb-3">{nextMilestone.title}</p>
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-blue-600">
                      还需 {nextMilestone.target_days - (stats?.current_streak || 0)} 天
                    </span>
                    <span className="text-blue-600">
                      {Math.round(((stats?.current_streak || 0) / nextMilestone.target_days) * 100)}%
                    </span>
                  </div>
                  <Progress 
                    value={((stats?.current_streak || 0) / nextMilestone.target_days) * 100} 
                    className="mt-2 h-2"
                  />
                </div>
              )}
            </CardContent>
          </Card>
        </div>
        </div>
      )}

      {/* 成就里程碑标签页 */}
      {activeTab === 'achievements' && (
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Trophy className="mr-2 h-5 w-5 text-yellow-500" />
                成就里程碑
              </CardTitle>
              <CardDescription>
                查看您已获得的成就和即将达成的目标
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {milestones.map((milestone) => (
                  <div
                    key={milestone.id}
                    className={`p-4 rounded-lg border ${
                      milestone.achieved
                        ? 'bg-yellow-50 border-yellow-200'
                        : 'bg-gray-50 border-gray-200'
                    }`}
                  >
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center">
                        {milestone.achieved ? (
                          <Trophy className="h-5 w-5 text-yellow-500 mr-2" />
                        ) : (
                          <Target className="h-5 w-5 text-gray-400 mr-2" />
                        )}
                        <span className={`text-sm font-medium ${
                          milestone.achieved ? 'text-yellow-700' : 'text-gray-600'
                        }`}>
                          {milestone.title}
                        </span>
                      </div>
                      {milestone.achieved && (
                        <Star className="h-4 w-4 text-yellow-500" />
                      )}
                    </div>
                    <p className="text-xs text-gray-600 mb-2">{milestone.description}</p>
                    <div className="flex items-center justify-between text-xs">
                      <span className="text-gray-500">目标: {milestone.target_days} 天</span>
                      {milestone.achieved && milestone.achieved_at && (
                        <span className="text-green-600">
                          {formatRelativeDate(milestone.achieved_at)}
                        </span>
                      )}
                    </div>
                    {!milestone.achieved && (
                      <div className="mt-2">
                        <div className="flex items-center justify-between text-xs mb-1">
                          <span className="text-gray-500">
                            进度: {stats?.current_streak || 0}/{milestone.target_days}
                          </span>
                          <span className="text-gray-500">
                            {Math.round(((stats?.current_streak || 0) / milestone.target_days) * 100)}%
                          </span>
                        </div>
                        <Progress
                          value={((stats?.current_streak || 0) / milestone.target_days) * 100}
                          className="h-1"
                        />
                      </div>
                    )}
                  </div>
                ))}
              </div>

              {nextMilestone && (
                <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                  <h4 className="font-semibold text-blue-700 mb-2">下一个目标</h4>
                  <p className="text-blue-600 text-sm mb-3">{nextMilestone.title}</p>
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-blue-600">
                      还需 {nextMilestone.target_days - (stats?.current_streak || 0)} 天
                    </span>
                    <span className="text-blue-600">
                      {Math.round(((stats?.current_streak || 0) / nextMilestone.target_days) * 100)}%
                    </span>
                  </div>
                  <Progress
                    value={((stats?.current_streak || 0) / nextMilestone.target_days) * 100}
                    className="mt-2 h-2"
                  />
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      )}

      {/* 邀请好友标签页 */}
      {activeTab === 'invite' && (
        <InviteCodeManager />
      )}

      {/* 社交数据标签页 */}
      {activeTab === 'social' && (
        <div className="space-y-6">
          {socialDataView === 'overview' ? (
            <>
              {/* 社交数据概览 */}
          {/* 个人徽章和编号 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <BadgeIcon className="mr-2 h-5 w-5 text-purple-500" />
                个人徽章与编号
              </CardTitle>
              <CardDescription>
                你的专属身份标识和等级信息
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div className="flex items-center justify-between p-4 bg-gradient-to-r from-purple-50 to-blue-50 rounded-lg">
                    <div>
                      <div className="text-sm text-gray-600">用户编号</div>
                      <div className="text-xl font-bold text-purple-600">{userBadge.id}</div>
                    </div>
                    <BadgeIcon className="h-8 w-8 text-purple-500" />
                  </div>
                  <div className="flex items-center justify-between p-4 bg-gradient-to-r from-yellow-50 to-orange-50 rounded-lg">
                    <div>
                      <div className="text-sm text-gray-600">会员等级</div>
                      <div className="text-xl font-bold text-yellow-600">{userBadge.level}</div>
                    </div>
                    <Star className="h-8 w-8 text-yellow-500" />
                  </div>
                </div>
                <div className="space-y-4">
                  <div className="p-4 bg-gray-50 rounded-lg">
                    <div className="text-sm text-gray-600 mb-1">加入时间</div>
                    <div className="font-medium">{userBadge.joinDate}</div>
                  </div>
                  <div className="p-4 bg-gray-50 rounded-lg">
                    <div className="text-sm text-gray-600 mb-1">最后活跃</div>
                    <div className="font-medium">{userBadge.lastActive}</div>
                  </div>
                  <Button className="w-full" variant="outline">
                    <Edit className="mr-2 h-4 w-4" />
                    编辑资料
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 社交统计数据 */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {/* 互动数据 */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center text-lg">
                  <Heart className="mr-2 h-5 w-5 text-red-500" />
                  互动数据
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <button
                    onClick={() => setSocialDataView('likes')}
                    className="w-full flex items-center justify-between p-3 rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    <div className="flex items-center">
                      <ThumbsUp className="mr-2 h-4 w-4 text-blue-500" />
                      <span className="text-sm text-gray-600">我的点赞</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className="font-semibold text-blue-600">{socialStats.givenLikes}</span>
                      <ChevronRight className="h-4 w-4 text-gray-400" />
                    </div>
                  </button>
                  <button
                    onClick={() => setSocialDataView('comments')}
                    className="w-full flex items-center justify-between p-3 rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    <div className="flex items-center">
                      <MessageCircle className="mr-2 h-4 w-4 text-green-500" />
                      <span className="text-sm text-gray-600">我的评论</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className="font-semibold text-green-600">{socialStats.totalComments}</span>
                      <ChevronRight className="h-4 w-4 text-gray-400" />
                    </div>
                  </button>
                  <div className="flex items-center justify-between p-3">
                    <div className="flex items-center">
                      <Heart className="mr-2 h-4 w-4 text-red-500" />
                      <span className="text-sm text-gray-600">获得点赞</span>
                    </div>
                    <span className="font-semibold text-red-600">{socialStats.receivedLikes}</span>
                  </div>
                  <div className="flex items-center justify-between p-3">
                    <div className="flex items-center">
                      <Eye className="mr-2 h-4 w-4 text-purple-500" />
                      <span className="text-sm text-gray-600">浏览量</span>
                    </div>
                    <span className="font-semibold text-purple-600">{socialStats.totalViews}</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* 内容数据 */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center text-lg">
                  <Bookmark className="mr-2 h-5 w-5 text-yellow-500" />
                  内容数据
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between p-3">
                    <div className="flex items-center">
                      <Edit className="mr-2 h-4 w-4 text-blue-500" />
                      <span className="text-sm text-gray-600">发布帖子</span>
                    </div>
                    <span className="font-semibold text-blue-600">{socialStats.postsCount}</span>
                  </div>
                  <button
                    onClick={() => setSocialDataView('bookmarks')}
                    className="w-full flex items-center justify-between p-3 rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    <div className="flex items-center">
                      <Bookmark className="mr-2 h-4 w-4 text-yellow-500" />
                      <span className="text-sm text-gray-600">我的收藏</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className="font-semibold text-yellow-600">{socialStats.totalBookmarks}</span>
                      <ChevronRight className="h-4 w-4 text-gray-400" />
                    </div>
                  </button>
                  <button
                    onClick={() => setSocialDataView('shares')}
                    className="w-full flex items-center justify-between p-3 rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    <div className="flex items-center">
                      <Share className="mr-2 h-4 w-4 text-green-500" />
                      <span className="text-sm text-gray-600">我的分享</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className="font-semibold text-green-600">{socialStats.totalShares}</span>
                      <ChevronRight className="h-4 w-4 text-gray-400" />
                    </div>
                  </button>
                </div>
              </CardContent>
            </Card>

            {/* 社交关系 */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center text-lg">
                  <Users className="mr-2 h-5 w-5 text-indigo-500" />
                  社交关系
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <Users className="mr-2 h-4 w-4 text-indigo-500" />
                      <span className="text-sm text-gray-600">关注者</span>
                    </div>
                    <span className="font-semibold text-indigo-600">{socialStats.followersCount}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <User className="mr-2 h-4 w-4 text-purple-500" />
                      <span className="text-sm text-gray-600">关注中</span>
                    </div>
                    <span className="font-semibold text-purple-600">{socialStats.followingCount}</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* 打赏支持 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Coffee className="mr-2 h-5 w-5 text-orange-500" />
                支持平台建设
              </CardTitle>
              <CardDescription>
                如果我们的平台对你有帮助，可以考虑打赏支持，帮助我们服务更多的人
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Button variant="outline" className="flex flex-col items-center p-6 h-auto">
                  <Coffee className="h-8 w-8 text-orange-500 mb-2" />
                  <span className="font-medium">请我喝咖啡</span>
                  <span className="text-sm text-gray-500">¥5</span>
                </Button>
                <Button variant="outline" className="flex flex-col items-center p-6 h-auto">
                  <Gift className="h-8 w-8 text-purple-500 mb-2" />
                  <span className="font-medium">小额支持</span>
                  <span className="text-sm text-gray-500">¥20</span>
                </Button>
                <Button variant="outline" className="flex flex-col items-center p-6 h-auto">
                  <Heart className="h-8 w-8 text-red-500 mb-2" />
                  <span className="font-medium">大力支持</span>
                  <span className="text-sm text-gray-500">¥50</span>
                </Button>
              </div>
              <div className="mt-4 p-4 bg-blue-50 rounded-lg">
                <p className="text-sm text-blue-800">
                  💡 你的支持将用于：服务器维护、功能开发、内容创作、帮助更多需要的人
                </p>
              </div>
            </CardContent>
          </Card>
            </>
          ) : (
            /* 社交数据列表页 */
            <SocialDataList
              type={socialDataView as 'likes' | 'bookmarks' | 'shares' | 'comments'}
              onBack={() => setSocialDataView('overview')}
            />
          )}
        </div>
      )}
    </div>
  )
}
