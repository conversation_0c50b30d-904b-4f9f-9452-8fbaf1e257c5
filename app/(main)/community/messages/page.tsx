'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { 
  MessageCircle,
  Send,
  Search,
  MoreVertical,
  Phone,
  Video,
  Info,
  Smile,
  Paperclip,
  Clock,
  Check,
  CheckCheck
} from 'lucide-react'
import { mockApiResponse } from '@/lib/mock-data'
import { format, subHours, subDays, subMinutes } from 'date-fns'

// 生成私信数据
function generateMessagesData() {
  const conversations = [
    {
      id: 'conv-1',
      participant: {
        id: 'user-1',
        name: '坚持的勇士',
        avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=user1',
        online: true,
        last_seen: new Date().toISOString()
      },
      last_message: {
        id: 'msg-1',
        content: '今天感觉压力很大，你是怎么应对的？',
        sender_id: 'user-1',
        timestamp: subMinutes(new Date(), 5).toISOString(),
        read: false
      },
      unread_count: 2,
      messages: [
        {
          id: 'msg-1',
          content: '今天感觉压力很大，你是怎么应对的？',
          sender_id: 'user-1',
          timestamp: subMinutes(new Date(), 5).toISOString(),
          read: false,
          type: 'text'
        },
        {
          id: 'msg-2',
          content: '我通常会去跑步或者听音乐来缓解压力',
          sender_id: 'current-user',
          timestamp: subMinutes(new Date(), 10).toISOString(),
          read: true,
          type: 'text'
        },
        {
          id: 'msg-3',
          content: '谢谢你的建议！我也试试运动',
          sender_id: 'user-1',
          timestamp: subMinutes(new Date(), 15).toISOString(),
          read: true,
          type: 'text'
        }
      ]
    },
    {
      id: 'conv-2',
      participant: {
        id: 'user-2',
        name: '阳光的行者',
        avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=user2',
        online: false,
        last_seen: subHours(new Date(), 2).toISOString()
      },
      last_message: {
        id: 'msg-4',
        content: '恭喜你坚持30天了！🎉',
        sender_id: 'current-user',
        timestamp: subHours(new Date(), 3).toISOString(),
        read: true
      },
      unread_count: 0,
      messages: [
        {
          id: 'msg-4',
          content: '恭喜你坚持30天了！🎉',
          sender_id: 'current-user',
          timestamp: subHours(new Date(), 3).toISOString(),
          read: true,
          type: 'text'
        },
        {
          id: 'msg-5',
          content: '谢谢！这都是大家的鼓励和支持',
          sender_id: 'user-2',
          timestamp: subHours(new Date(), 4).toISOString(),
          read: true,
          type: 'text'
        }
      ]
    },
    {
      id: 'conv-3',
      participant: {
        id: 'user-3',
        name: '智慧的学者',
        avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=user3',
        online: true,
        last_seen: new Date().toISOString()
      },
      last_message: {
        id: 'msg-6',
        content: '有什么好的学习资料推荐吗？',
        sender_id: 'user-3',
        timestamp: subDays(new Date(), 1).toISOString(),
        read: true
      },
      unread_count: 0,
      messages: [
        {
          id: 'msg-6',
          content: '有什么好的学习资料推荐吗？',
          sender_id: 'user-3',
          timestamp: subDays(new Date(), 1).toISOString(),
          read: true,
          type: 'text'
        }
      ]
    }
  ]

  return conversations
}

export default function MessagesPage() {
  const [conversations, setConversations] = useState<any[]>([])
  const [selectedConversation, setSelectedConversation] = useState<any>(null)
  const [newMessage, setNewMessage] = useState('')
  const [loading, setLoading] = useState(false)
  const [searchQuery, setSearchQuery] = useState('')

  const loadMessages = async () => {
    setLoading(true)
    try {
      const response = await mockApiResponse(generateMessagesData())
      setConversations(response.data)
      if (response.data.length > 0) {
        setSelectedConversation(response.data[0])
      }
    } catch (error) {
      console.error('加载消息失败:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    loadMessages()
  }, [])

  const sendMessage = () => {
    if (!newMessage.trim() || !selectedConversation) return

    const message = {
      id: `msg-${Date.now()}`,
      content: newMessage,
      sender_id: 'current-user',
      timestamp: new Date().toISOString(),
      read: false,
      type: 'text'
    }

    // 更新选中的对话
    const updatedConversation = {
      ...selectedConversation,
      messages: [...selectedConversation.messages, message],
      last_message: message
    }

    // 更新对话列表
    const updatedConversations = conversations.map(conv =>
      conv.id === selectedConversation.id ? updatedConversation : conv
    )

    setConversations(updatedConversations)
    setSelectedConversation(updatedConversation)
    setNewMessage('')
  }

  const formatMessageTime = (timestamp: string) => {
    const date = new Date(timestamp)
    const now = new Date()
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60)

    if (diffInHours < 1) {
      return format(date, 'HH:mm')
    } else if (diffInHours < 24) {
      return format(date, 'HH:mm')
    } else {
      return format(date, 'MM/dd HH:mm')
    }
  }

  const getLastSeenText = (lastSeen: string, online: boolean) => {
    if (online) return '在线'
    
    const date = new Date(lastSeen)
    const now = new Date()
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60)

    if (diffInHours < 1) {
      return '刚刚在线'
    } else if (diffInHours < 24) {
      return `${Math.floor(diffInHours)}小时前在线`
    } else {
      return `${Math.floor(diffInHours / 24)}天前在线`
    }
  }

  const filteredConversations = conversations.filter(conv =>
    conv.participant.name.toLowerCase().includes(searchQuery.toLowerCase())
  )

  return (
    <div className="h-[calc(100vh-8rem)] flex">
      {/* 对话列表 */}
      <div className="w-1/3 border-r bg-white">
        <div className="p-4 border-b">
          <h1 className="text-xl font-bold text-gray-900 flex items-center mb-4">
            <MessageCircle className="mr-2 h-5 w-5 text-blue-500" />
            私信
          </h1>
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder="搜索对话..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>

        <div className="overflow-y-auto h-full">
          {filteredConversations.map((conversation) => (
            <div
              key={conversation.id}
              className={`p-4 border-b cursor-pointer hover:bg-gray-50 transition-colors ${
                selectedConversation?.id === conversation.id ? 'bg-blue-50 border-blue-200' : ''
              }`}
              onClick={() => setSelectedConversation(conversation)}
            >
              <div className="flex items-start space-x-3">
                <div className="relative">
                  <Avatar className="h-12 w-12">
                    <AvatarImage src={conversation.participant.avatar} alt={conversation.participant.name} />
                    <AvatarFallback>{conversation.participant.name.slice(0, 2)}</AvatarFallback>
                  </Avatar>
                  {conversation.participant.online && (
                    <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-green-500 border-2 border-white rounded-full"></div>
                  )}
                </div>
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between mb-1">
                    <h3 className="font-medium text-gray-900 truncate">
                      {conversation.participant.name}
                    </h3>
                    <span className="text-xs text-gray-500">
                      {formatMessageTime(conversation.last_message.timestamp)}
                    </span>
                  </div>
                  <p className="text-sm text-gray-600 truncate">
                    {conversation.last_message.content}
                  </p>
                  <div className="flex items-center justify-between mt-1">
                    <span className="text-xs text-gray-400">
                      {getLastSeenText(conversation.participant.last_seen, conversation.participant.online)}
                    </span>
                    {conversation.unread_count > 0 && (
                      <Badge className="bg-red-500 text-white text-xs px-2 py-1">
                        {conversation.unread_count}
                      </Badge>
                    )}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* 聊天区域 */}
      <div className="flex-1 flex flex-col">
        {selectedConversation ? (
          <>
            {/* 聊天头部 */}
            <div className="p-4 border-b bg-white flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="relative">
                  <Avatar className="h-10 w-10">
                    <AvatarImage src={selectedConversation.participant.avatar} alt={selectedConversation.participant.name} />
                    <AvatarFallback>{selectedConversation.participant.name.slice(0, 2)}</AvatarFallback>
                  </Avatar>
                  {selectedConversation.participant.online && (
                    <div className="absolute -bottom-1 -right-1 w-3 h-3 bg-green-500 border-2 border-white rounded-full"></div>
                  )}
                </div>
                <div>
                  <h3 className="font-medium text-gray-900">{selectedConversation.participant.name}</h3>
                  <p className="text-sm text-gray-500">
                    {getLastSeenText(selectedConversation.participant.last_seen, selectedConversation.participant.online)}
                  </p>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <Button size="sm" variant="ghost">
                  <Phone className="h-4 w-4" />
                </Button>
                <Button size="sm" variant="ghost">
                  <Video className="h-4 w-4" />
                </Button>
                <Button size="sm" variant="ghost">
                  <Info className="h-4 w-4" />
                </Button>
                <Button size="sm" variant="ghost">
                  <MoreVertical className="h-4 w-4" />
                </Button>
              </div>
            </div>

            {/* 消息列表 */}
            <div className="flex-1 overflow-y-auto p-4 space-y-4 bg-gray-50">
              {selectedConversation.messages.map((message: any) => (
                <div
                  key={message.id}
                  className={`flex ${message.sender_id === 'current-user' ? 'justify-end' : 'justify-start'}`}
                >
                  <div className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                    message.sender_id === 'current-user'
                      ? 'bg-blue-500 text-white'
                      : 'bg-white text-gray-900 border'
                  }`}>
                    <p className="text-sm">{message.content}</p>
                    <div className={`flex items-center justify-end mt-1 space-x-1 ${
                      message.sender_id === 'current-user' ? 'text-blue-100' : 'text-gray-400'
                    }`}>
                      <span className="text-xs">{formatMessageTime(message.timestamp)}</span>
                      {message.sender_id === 'current-user' && (
                        message.read ? <CheckCheck className="h-3 w-3" /> : <Check className="h-3 w-3" />
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* 消息输入 */}
            <div className="p-4 border-t bg-white">
              <div className="flex items-center space-x-2">
                <Button size="sm" variant="ghost">
                  <Paperclip className="h-4 w-4" />
                </Button>
                <div className="flex-1 relative">
                  <Input
                    placeholder="输入消息..."
                    value={newMessage}
                    onChange={(e) => setNewMessage(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && sendMessage()}
                    className="pr-10"
                  />
                  <Button
                    size="sm"
                    variant="ghost"
                    className="absolute right-1 top-1/2 transform -translate-y-1/2"
                  >
                    <Smile className="h-4 w-4" />
                  </Button>
                </div>
                <Button onClick={sendMessage} disabled={!newMessage.trim()}>
                  <Send className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </>
        ) : (
          <div className="flex-1 flex items-center justify-center bg-gray-50">
            <div className="text-center">
              <MessageCircle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">选择一个对话</h3>
              <p className="text-gray-500">从左侧选择一个对话开始聊天</p>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
