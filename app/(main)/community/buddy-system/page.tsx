'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { 
  Users,
  UserPlus,
  MessageCircle,
  Target,
  Calendar,
  MapPin,
  Clock,
  Star,
  Shield,
  Heart,
  Handshake,
  RefreshCw,
  Search,
  Filter
} from 'lucide-react'
import { mockApiResponse } from '@/lib/mock-data'

// 生成戒友数据
function generateBuddyData() {
  const buddies = [
    {
      id: 'buddy-1',
      name: '坚持的勇士',
      avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=buddy1',
      age: 25,
      location: '北京',
      streak_days: 45,
      experience_level: 'intermediate',
      goals: ['戒色', '健康生活', '自我提升'],
      interests: ['运动', '阅读', '冥想'],
      availability: ['晚上', '周末'],
      timezone: 'GMT+8',
      match_score: 92,
      last_active: '2小时前',
      bio: '正在努力改变自己，希望找到志同道合的伙伴一起进步。喜欢运动和阅读，相信坚持的力量。',
      supervision_style: '温和鼓励',
      preferred_contact: '每日签到',
      success_stories: 3,
      helped_users: 12
    },
    {
      id: 'buddy-2',
      name: '阳光的行者',
      avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=buddy2',
      age: 28,
      location: '上海',
      streak_days: 78,
      experience_level: 'advanced',
      goals: ['戒色', '事业发展', '人际关系'],
      interests: ['健身', '摄影', '旅行'],
      availability: ['早晨', '晚上'],
      timezone: 'GMT+8',
      match_score: 88,
      last_active: '30分钟前',
      bio: '戒色78天，经历过起伏但从未放弃。愿意分享经验，帮助新人度过困难期。',
      supervision_style: '严格督促',
      preferred_contact: '每周通话',
      success_stories: 8,
      helped_users: 25
    },
    {
      id: 'buddy-3',
      name: '智慧的学者',
      avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=buddy3',
      age: 22,
      location: '广州',
      streak_days: 23,
      experience_level: 'beginner',
      goals: ['戒色', '学业进步', '身心健康'],
      interests: ['学习', '音乐', '编程'],
      availability: ['下午', '晚上'],
      timezone: 'GMT+8',
      match_score: 85,
      last_active: '1小时前',
      bio: '大学生，刚开始戒色之路。希望找到同龄的伙伴互相鼓励，一起成长。',
      supervision_style: '互相鼓励',
      preferred_contact: '随时聊天',
      success_stories: 1,
      helped_users: 3
    },
    {
      id: 'buddy-4',
      name: '沉稳的导师',
      avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=buddy4',
      age: 32,
      location: '深圳',
      streak_days: 156,
      experience_level: 'expert',
      goals: ['戒色', '家庭和谐', '心灵成长'],
      interests: ['冥想', '哲学', '心理学'],
      availability: ['早晨', '周末'],
      timezone: 'GMT+8',
      match_score: 90,
      last_active: '15分钟前',
      bio: '戒色经验丰富，愿意做新人的导师。相信每个人都有改变的潜力，愿意耐心指导。',
      supervision_style: '导师指导',
      preferred_contact: '定期指导',
      success_stories: 15,
      helped_users: 48
    }
  ]

  return buddies
}

export default function BuddySystemPage() {
  const [buddies, setBuddies] = useState<any[]>([])
  const [loading, setLoading] = useState(false)
  const [selectedBuddy, setSelectedBuddy] = useState<any>(null)
  const [filterLevel, setFilterLevel] = useState<string>('all')

  const loadBuddies = async () => {
    setLoading(true)
    try {
      const response = await mockApiResponse(generateBuddyData())
      setBuddies(response.data)
    } catch (error) {
      console.error('加载戒友数据失败:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    loadBuddies()
  }, [])

  const getExperienceLabel = (level: string) => {
    switch (level) {
      case 'beginner': return '新手'
      case 'intermediate': return '进阶'
      case 'advanced': return '高级'
      case 'expert': return '专家'
      default: return '未知'
    }
  }

  const getExperienceColor = (level: string) => {
    switch (level) {
      case 'beginner': return 'bg-green-100 text-green-800'
      case 'intermediate': return 'bg-blue-100 text-blue-800'
      case 'advanced': return 'bg-purple-100 text-purple-800'
      case 'expert': return 'bg-yellow-100 text-yellow-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const filteredBuddies = filterLevel === 'all' 
    ? buddies 
    : buddies.filter(buddy => buddy.experience_level === filterLevel)

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 flex items-center">
            <Users className="mr-2 h-6 w-6 text-blue-500" />
            戒友配对系统
          </h1>
          <p className="text-gray-600">找到志同道合的戒友，互相监督，共同成长</p>
        </div>
        <Button 
          onClick={loadBuddies} 
          disabled={loading}
          variant="gradient"
        >
          <RefreshCw className={`mr-2 h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
          {loading ? '刷新中...' : '刷新推荐'}
        </Button>
      </div>

      {/* 统计概览 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">在线戒友</CardTitle>
            <Users className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">1,234</div>
            <p className="text-xs text-muted-foreground">
              当前在线
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">成功配对</CardTitle>
            <Handshake className="h-4 w-4 text-blue-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">856</div>
            <p className="text-xs text-muted-foreground">
              本月新增 +127
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">互助次数</CardTitle>
            <Heart className="h-4 w-4 text-red-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">3,421</div>
            <p className="text-xs text-muted-foreground">
              今日互助
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">成功率</CardTitle>
            <Target className="h-4 w-4 text-purple-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">87%</div>
            <p className="text-xs text-muted-foreground">
              配对成功率
            </p>
          </CardContent>
        </Card>
      </div>

      {/* 筛选器 */}
      <div className="flex flex-wrap gap-2">
        <Button
          variant={filterLevel === 'all' ? "default" : "outline"}
          size="sm"
          onClick={() => setFilterLevel('all')}
        >
          全部
        </Button>
        <Button
          variant={filterLevel === 'beginner' ? "default" : "outline"}
          size="sm"
          onClick={() => setFilterLevel('beginner')}
        >
          新手
        </Button>
        <Button
          variant={filterLevel === 'intermediate' ? "default" : "outline"}
          size="sm"
          onClick={() => setFilterLevel('intermediate')}
        >
          进阶
        </Button>
        <Button
          variant={filterLevel === 'advanced' ? "default" : "outline"}
          size="sm"
          onClick={() => setFilterLevel('advanced')}
        >
          高级
        </Button>
        <Button
          variant={filterLevel === 'expert' ? "default" : "outline"}
          size="sm"
          onClick={() => setFilterLevel('expert')}
        >
          专家
        </Button>
      </div>

      {/* 戒友列表 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {filteredBuddies.map((buddy) => (
          <Card key={buddy.id} className="hover:shadow-lg transition-shadow">
            <CardContent className="p-6">
              {/* 基本信息 */}
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center space-x-4">
                  <Avatar className="h-16 w-16">
                    <AvatarImage src={buddy.avatar} alt={buddy.name} />
                    <AvatarFallback>{buddy.name.slice(0, 2)}</AvatarFallback>
                  </Avatar>
                  <div>
                    <h3 className="font-semibold text-lg">{buddy.name}</h3>
                    <div className="flex items-center gap-2 text-sm text-gray-500">
                      <MapPin className="h-4 w-4" />
                      {buddy.location} · {buddy.age}岁
                    </div>
                    <div className="flex items-center gap-2 text-sm text-gray-500">
                      <Clock className="h-4 w-4" />
                      {buddy.last_active}
                    </div>
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-2xl font-bold text-orange-600">{buddy.streak_days}</div>
                  <div className="text-xs text-gray-500">连续天数</div>
                </div>
              </div>

              {/* 标签 */}
              <div className="flex flex-wrap gap-2 mb-4">
                <Badge className={getExperienceColor(buddy.experience_level)}>
                  {getExperienceLabel(buddy.experience_level)}
                </Badge>
                <Badge variant="outline">
                  匹配度 {buddy.match_score}%
                </Badge>
                <Badge variant="outline">
                  帮助过 {buddy.helped_users} 人
                </Badge>
              </div>

              {/* 个人简介 */}
              <p className="text-sm text-gray-600 mb-4 line-clamp-2">
                {buddy.bio}
              </p>

              {/* 详细信息 */}
              <div className="space-y-2 mb-4">
                <div className="flex items-center gap-2 text-sm">
                  <Target className="h-4 w-4 text-blue-500" />
                  <span className="text-gray-600">目标：</span>
                  <span>{buddy.goals.slice(0, 2).join(', ')}</span>
                </div>
                <div className="flex items-center gap-2 text-sm">
                  <Heart className="h-4 w-4 text-red-500" />
                  <span className="text-gray-600">兴趣：</span>
                  <span>{buddy.interests.slice(0, 2).join(', ')}</span>
                </div>
                <div className="flex items-center gap-2 text-sm">
                  <Calendar className="h-4 w-4 text-green-500" />
                  <span className="text-gray-600">可用时间：</span>
                  <span>{buddy.availability.join(', ')}</span>
                </div>
                <div className="flex items-center gap-2 text-sm">
                  <Shield className="h-4 w-4 text-purple-500" />
                  <span className="text-gray-600">监督风格：</span>
                  <span>{buddy.supervision_style}</span>
                </div>
              </div>

              {/* 操作按钮 */}
              <div className="flex gap-2">
                <Button size="sm" className="flex-1">
                  <UserPlus className="mr-2 h-4 w-4" />
                  申请配对
                </Button>
                <Button size="sm" variant="outline">
                  <MessageCircle className="mr-2 h-4 w-4" />
                  发消息
                </Button>
                <Button size="sm" variant="ghost">
                  查看详情
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredBuddies.length === 0 && !loading && (
        <Card>
          <CardContent className="text-center py-12">
            <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">暂无匹配的戒友</h3>
            <p className="text-gray-500">请尝试调整筛选条件或稍后再试</p>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
