'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'

export default function HomePage() {
  const router = useRouter()

  useEffect(() => {
    // 检查用户是否已登录
    const mockUser = localStorage.getItem('mock-user')
    const mockSession = localStorage.getItem('mock-session')
    
    if (mockUser && mockSession) {
      // 已登录，跳转到仪表盘
      router.push('/dashboard')
    } else {
      // 未登录，跳转到登录页
      router.push('/login')
    }
  }, [router])

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-purple-50">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <p className="text-gray-600">正在加载...</p>
      </div>
    </div>
  )
}
