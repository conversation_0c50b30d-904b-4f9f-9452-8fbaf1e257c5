'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { Skeleton } from '@/components/ui/skeleton'

export default function HomePage() {
  const router = useRouter()

  useEffect(() => {
    // 检查用户是否已登录
    const mockUser = localStorage.getItem('mock-user')
    const mockSession = localStorage.getItem('mock-session')
    
    if (mockUser && mockSession) {
      // 已登录，跳转到仪表盘
      router.push('/dashboard')
    } else {
      // 未登录，跳转到登录页
      router.push('/login')
    }
  }, [router])

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-purple-50">
      <div className="w-full max-w-md space-y-4">
        <div className="text-center mb-8">
          <Skeleton className="h-8 w-32 mx-auto mb-2" />
          <Skeleton className="h-4 w-48 mx-auto" />
        </div>
        <div className="space-y-3">
          <Skeleton className="h-12 w-full" />
          <Skeleton className="h-12 w-full" />
          <Skeleton className="h-10 w-full" />
        </div>
      </div>
    </div>
  )
}
