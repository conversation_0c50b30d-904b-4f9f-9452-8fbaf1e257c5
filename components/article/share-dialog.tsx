'use client'

import { useState } from 'react'
import { <PERSON>alog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { 
  Share2,
  Copy,
  MessageCircle,
  Mail,
  Link,
  QrCode,
  Check,
  Smartphone,
  Globe
} from 'lucide-react'

interface ShareDialogProps {
  title: string
  content: string
  articleId: string
  author?: string
}

export default function ShareDialog({ title, content, articleId, author }: ShareDialogProps) {
  const [open, setOpen] = useState(false)
  const [copied, setCopied] = useState<string | null>(null)

  // 生成分享链接
  const baseUrl = typeof window !== 'undefined' ? window.location.origin : 'https://upbase.app'
  const shareUrl = `${baseUrl}/article/${articleId}`
  
  // 生成不同平台的分享链接
  const shareLinks = {
    wechat: `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${encodeURIComponent(shareUrl)}`,
    weibo: `https://service.weibo.com/share/share.php?url=${encodeURIComponent(shareUrl)}&title=${encodeURIComponent(title)}&content=${encodeURIComponent(content.slice(0, 100))}`,
    qq: `https://connect.qq.com/widget/shareqq/index.html?url=${encodeURIComponent(shareUrl)}&title=${encodeURIComponent(title)}&summary=${encodeURIComponent(content.slice(0, 100))}`,
    email: `mailto:?subject=${encodeURIComponent(title)}&body=${encodeURIComponent(`推荐一篇好文章：${title}\n\n${content.slice(0, 200)}...\n\n查看全文：${shareUrl}`)}`
  }

  const copyToClipboard = async (text: string, type: string) => {
    try {
      await navigator.clipboard.writeText(text)
      setCopied(type)
      setTimeout(() => setCopied(null), 2000)
    } catch (error) {
      console.error('复制失败:', error)
      // 降级方案
      const textArea = document.createElement('textarea')
      textArea.value = text
      document.body.appendChild(textArea)
      textArea.select()
      document.execCommand('copy')
      document.body.removeChild(textArea)
      setCopied(type)
      setTimeout(() => setCopied(null), 2000)
    }
  }

  const shareToNative = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: title,
          text: content.slice(0, 100) + '...',
          url: shareUrl
        })
      } catch (error) {
        console.error('分享失败:', error)
      }
    } else {
      // 降级到复制链接
      copyToClipboard(shareUrl, 'link')
    }
  }

  const generateShareText = () => {
    return `📖 推荐阅读：${title}\n\n${content.slice(0, 150)}...\n\n👆 点击链接查看全文：${shareUrl}\n\n${author ? `作者：${author}` : ''}\n\n#戒色社区 #正能量分享`
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="ghost" size="sm">
          <Share2 className="h-5 w-5 mr-2" />
          分享
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>分享文章</DialogTitle>
          <DialogDescription>
            将这篇好文章分享给更多人
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* 快速分享 */}
          <div className="space-y-3">
            <Label className="text-sm font-medium">快速分享</Label>
            <div className="grid grid-cols-2 gap-3">
              <Button
                variant="outline"
                className="h-12 flex flex-col items-center justify-center space-y-1"
                onClick={shareToNative}
              >
                <Smartphone className="h-5 w-5" />
                <span className="text-xs">系统分享</span>
              </Button>
              <Button
                variant="outline"
                className="h-12 flex flex-col items-center justify-center space-y-1"
                onClick={() => window.open(shareLinks.weibo, '_blank')}
              >
                <Globe className="h-5 w-5" />
                <span className="text-xs">微博</span>
              </Button>
              <Button
                variant="outline"
                className="h-12 flex flex-col items-center justify-center space-y-1"
                onClick={() => window.open(shareLinks.qq, '_blank')}
              >
                <MessageCircle className="h-5 w-5" />
                <span className="text-xs">QQ空间</span>
              </Button>
              <Button
                variant="outline"
                className="h-12 flex flex-col items-center justify-center space-y-1"
                onClick={() => window.open(shareLinks.email)}
              >
                <Mail className="h-5 w-5" />
                <span className="text-xs">邮件</span>
              </Button>
            </div>
          </div>

          {/* 微信分享二维码 */}
          <div className="space-y-3">
            <Label className="text-sm font-medium">微信分享</Label>
            <div className="flex flex-col items-center p-4 bg-gray-50 rounded-lg">
              <img 
                src={shareLinks.wechat} 
                alt="微信分享二维码" 
                className="w-32 h-32 mb-2"
              />
              <p className="text-xs text-gray-600 text-center">
                使用微信扫描二维码分享
              </p>
            </div>
          </div>

          {/* 复制链接 */}
          <div className="space-y-3">
            <Label className="text-sm font-medium">复制链接</Label>
            <div className="flex space-x-2">
              <Input
                value={shareUrl}
                readOnly
                className="flex-1 text-sm"
              />
              <Button
                variant="outline"
                size="sm"
                onClick={() => copyToClipboard(shareUrl, 'link')}
              >
                {copied === 'link' ? (
                  <Check className="h-4 w-4" />
                ) : (
                  <Copy className="h-4 w-4" />
                )}
              </Button>
            </div>
          </div>

          {/* 复制分享文案 */}
          <div className="space-y-3">
            <Label className="text-sm font-medium">复制分享文案</Label>
            <div className="p-3 bg-gray-50 rounded-lg">
              <p className="text-sm text-gray-700 mb-2 line-clamp-4">
                {generateShareText()}
              </p>
              <Button
                variant="outline"
                size="sm"
                className="w-full"
                onClick={() => copyToClipboard(generateShareText(), 'text')}
              >
                {copied === 'text' ? (
                  <>
                    <Check className="h-4 w-4 mr-2" />
                    已复制
                  </>
                ) : (
                  <>
                    <Copy className="h-4 w-4 mr-2" />
                    复制文案
                  </>
                )}
              </Button>
            </div>
          </div>

          {/* 分享统计 */}
          <div className="text-center">
            <p className="text-xs text-gray-500">
              此文章已被分享 {Math.floor(Math.random() * 50) + 10} 次
            </p>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
