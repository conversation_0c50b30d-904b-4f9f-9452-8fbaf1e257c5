'use client'

import { useState } from 'react'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  Coffee,
  Gift,
  Heart,
  Star,
  Crown,
  Zap,
  Award,
  Users,
  TrendingUp,
  CheckCircle
} from 'lucide-react'
import { mockApiResponse } from '@/lib/mock-data'

interface RewardOption {
  id: string
  name: string
  amount: number
  icon: any
  color: string
  description: string
  popular?: boolean
}

interface RewardSystemProps {
  authorId: string
  authorName: string
  postId: string
  postType: 'article' | 'post' | 'inspiration'
  postTitle: string
}

export default function RewardSystem({ 
  authorId, 
  authorName, 
  postId, 
  postType, 
  postTitle 
}: RewardSystemProps) {
  const [selectedAmount, setSelectedAmount] = useState<number | null>(null)
  const [customAmount, setCustomAmount] = useState('')
  const [message, setMessage] = useState('')
  const [showSuccess, setShowSuccess] = useState(false)
  const [loading, setLoading] = useState(false)
  const [rewardStats, setRewardStats] = useState({
    totalRewards: 1247,
    totalAmount: 8960,
    recentRewards: 23
  })

  const rewardOptions: RewardOption[] = [
    {
      id: 'coffee',
      name: '请喝咖啡',
      amount: 5,
      icon: Coffee,
      color: 'from-amber-500 to-orange-600',
      description: '感谢分享，请作者喝杯咖啡'
    },
    {
      id: 'like',
      name: '小小心意',
      amount: 10,
      icon: Heart,
      color: 'from-pink-500 to-red-600',
      description: '表达一点心意和支持',
      popular: true
    },
    {
      id: 'support',
      name: '大力支持',
      amount: 20,
      icon: Star,
      color: 'from-blue-500 to-indigo-600',
      description: '非常有帮助，大力支持！'
    },
    {
      id: 'premium',
      name: '超级赞赏',
      amount: 50,
      icon: Crown,
      color: 'from-purple-500 to-pink-600',
      description: '内容太棒了，超级赞赏'
    },
    {
      id: 'vip',
      name: 'VIP打赏',
      amount: 100,
      icon: Zap,
      color: 'from-yellow-500 to-orange-600',
      description: '顶级支持，助力创作'
    }
  ]

  const handleReward = async () => {
    const amount = selectedAmount || parseInt(customAmount)
    if (!amount || amount < 1) {
      alert('请选择或输入打赏金额')
      return
    }

    setLoading(true)
    try {
      await mockApiResponse({ 
        success: true, 
        amount, 
        message,
        authorId,
        postId 
      })
      
      // 更新统计数据
      setRewardStats(prev => ({
        totalRewards: prev.totalRewards + 1,
        totalAmount: prev.totalAmount + amount,
        recentRewards: prev.recentRewards + 1
      }))
      
      setShowSuccess(true)
      setTimeout(() => {
        setShowSuccess(false)
        setSelectedAmount(null)
        setCustomAmount('')
        setMessage('')
      }, 3000)
    } catch (error) {
      console.error('打赏失败:', error)
      alert('打赏失败，请稍后重试')
    } finally {
      setLoading(false)
    }
  }

  if (showSuccess) {
    return (
      <Card className="border-green-200 bg-gradient-to-r from-green-50 to-emerald-50">
        <CardContent className="p-6 text-center">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-green-100 rounded-full mb-4">
            <CheckCircle className="h-8 w-8 text-green-500" />
          </div>
          <h3 className="text-lg font-semibold text-gray-900 mb-2">打赏成功！</h3>
          <p className="text-gray-600 mb-4">
            感谢您对 {authorName} 的支持，您的鼓励是创作者最大的动力！
          </p>
          <div className="flex items-center justify-center gap-4 text-sm text-gray-500">
            <span>💰 {selectedAmount || customAmount} 元</span>
            <span>📝 {postTitle}</span>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center">
            <Gift className="mr-2 h-5 w-5 text-orange-500" />
            打赏作者
          </div>
          <div className="flex items-center gap-4 text-sm text-gray-500">
            <span className="flex items-center">
              <Users className="mr-1 h-4 w-4" />
              {rewardStats.totalRewards}人
            </span>
            <span className="flex items-center">
              <TrendingUp className="mr-1 h-4 w-4" />
              ¥{rewardStats.totalAmount}
            </span>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {/* 作者信息 */}
          <div className="p-4 bg-gray-50 rounded-lg">
            <div className="flex items-center gap-3 mb-2">
              <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-semibold">
                {authorName.charAt(0)}
              </div>
              <div>
                <div className="font-medium text-gray-900">{authorName}</div>
                <div className="text-sm text-gray-500">优质内容创作者</div>
              </div>
            </div>
            <p className="text-sm text-gray-600">
              如果这篇《{postTitle}》对您有帮助，可以打赏支持作者继续创作优质内容
            </p>
          </div>

          {/* 打赏选项 */}
          <div>
            <h4 className="font-medium text-gray-900 mb-3">选择打赏金额</h4>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
              {rewardOptions.map((option) => {
                const Icon = option.icon
                return (
                  <button
                    key={option.id}
                    onClick={() => {
                      setSelectedAmount(option.amount)
                      setCustomAmount('')
                    }}
                    className={`relative p-4 border-2 rounded-lg transition-all hover:shadow-md ${
                      selectedAmount === option.amount
                        ? 'border-blue-500 bg-blue-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                  >
                    {option.popular && (
                      <Badge className="absolute -top-2 -right-2 bg-red-500 text-white text-xs">
                        热门
                      </Badge>
                    )}
                    <div className={`w-8 h-8 rounded-full bg-gradient-to-r ${option.color} flex items-center justify-center mb-2 mx-auto`}>
                      <Icon className="h-4 w-4 text-white" />
                    </div>
                    <div className="text-center">
                      <div className="font-medium text-gray-900 text-sm">{option.name}</div>
                      <div className="text-lg font-bold text-gray-900">¥{option.amount}</div>
                      <div className="text-xs text-gray-500 mt-1">{option.description}</div>
                    </div>
                  </button>
                )
              })}
            </div>
          </div>

          {/* 自定义金额 */}
          <div>
            <h4 className="font-medium text-gray-900 mb-3">或输入自定义金额</h4>
            <div className="flex items-center gap-2">
              <span className="text-gray-500">¥</span>
              <input
                type="number"
                min="1"
                max="1000"
                placeholder="输入金额"
                value={customAmount}
                onChange={(e) => {
                  setCustomAmount(e.target.value)
                  setSelectedAmount(null)
                }}
                className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              <span className="text-sm text-gray-500">元 (1-1000)</span>
            </div>
          </div>

          {/* 打赏留言 */}
          <div>
            <h4 className="font-medium text-gray-900 mb-3">留言给作者 (可选)</h4>
            <textarea
              placeholder="写下您的鼓励和建议..."
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none"
              maxLength={200}
            />
            <div className="text-right text-xs text-gray-500 mt-1">
              {message.length}/200
            </div>
          </div>

          {/* 打赏统计 */}
          <div className="p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg">
            <h4 className="font-medium text-gray-900 mb-3 flex items-center">
              <Award className="mr-2 h-4 w-4 text-blue-500" />
              打赏统计
            </h4>
            <div className="grid grid-cols-3 gap-4 text-center">
              <div>
                <div className="text-lg font-bold text-blue-600">{rewardStats.totalRewards}</div>
                <div className="text-xs text-gray-600">总打赏人数</div>
              </div>
              <div>
                <div className="text-lg font-bold text-green-600">¥{rewardStats.totalAmount}</div>
                <div className="text-xs text-gray-600">累计金额</div>
              </div>
              <div>
                <div className="text-lg font-bold text-orange-600">{rewardStats.recentRewards}</div>
                <div className="text-xs text-gray-600">本周新增</div>
              </div>
            </div>
          </div>

          {/* 打赏按钮 */}
          <Button 
            onClick={handleReward}
            disabled={(!selectedAmount && !customAmount) || loading}
            className="w-full bg-gradient-to-r from-orange-500 to-red-600 hover:from-orange-600 hover:to-red-700"
          >
            {loading ? (
              <div className="flex items-center">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                处理中...
              </div>
            ) : (
              <div className="flex items-center">
                <Gift className="mr-2 h-4 w-4" />
                打赏 ¥{selectedAmount || customAmount || 0}
              </div>
            )}
          </Button>

          {/* 说明文字 */}
          <div className="text-xs text-gray-500 text-center">
            <p>💡 打赏是对优质内容的认可和支持</p>
            <p>🔒 支付安全由第三方平台保障</p>
            <p>📧 打赏记录将发送到您的邮箱</p>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
