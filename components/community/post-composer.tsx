'use client'

import { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Textarea } from '@/components/ui/textarea'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { 
  Send,
  Image,
  Smile,
  Hash,
  X,
  Plus,
  Globe,
  Lock,
  Users
} from 'lucide-react'

interface PostComposerProps {
  onPost: (postData: any) => void
  onCancel?: () => void
}

export default function PostComposer({ onPost, onCancel }: PostComposerProps) {
  const [content, setContent] = useState('')
  const [tags, setTags] = useState<string[]>([])
  const [newTag, setNewTag] = useState('')
  const [privacy, setPrivacy] = useState<'public' | 'friends' | 'private'>('public')
  const [isAnonymous, setIsAnonymous] = useState(false)
  const [postType, setPostType] = useState<'text' | 'milestone' | 'question' | 'support'>('text')

  const predefinedTags = [
    '新手求助', '经验分享', '每日打卡', '心得体会', 
    '挫折经历', '成功故事', '方法推荐', '情绪管理',
    '目标设定', '习惯养成', '正能量', '互相鼓励'
  ]

  const postTypes = [
    { value: 'text', label: '普通动态', icon: '📝', color: 'bg-blue-100 text-blue-800' },
    { value: 'milestone', label: '里程碑', icon: '🏆', color: 'bg-yellow-100 text-yellow-800' },
    { value: 'question', label: '求助问题', icon: '❓', color: 'bg-red-100 text-red-800' },
    { value: 'support', label: '支持鼓励', icon: '💪', color: 'bg-green-100 text-green-800' }
  ]

  const addTag = (tag: string) => {
    if (tag && !tags.includes(tag) && tags.length < 5) {
      setTags([...tags, tag])
      setNewTag('')
    }
  }

  const removeTag = (tagToRemove: string) => {
    setTags(tags.filter(tag => tag !== tagToRemove))
  }

  const handleSubmit = () => {
    if (!content.trim()) return

    const postData = {
      id: `post-${Date.now()}`,
      content: content.trim(),
      tags,
      privacy,
      is_anonymous: isAnonymous,
      post_type: postType,
      created_at: new Date().toISOString(),
      author: isAnonymous ? '匿名用户' : '当前用户',
      likes: 0,
      comments: 0,
      shares: 0
    }

    onPost(postData)
    
    // 重置表单
    setContent('')
    setTags([])
    setNewTag('')
    setPrivacy('public')
    setIsAnonymous(false)
    setPostType('text')
  }

  const getPrivacyIcon = () => {
    switch (privacy) {
      case 'public': return <Globe className="h-4 w-4" />
      case 'friends': return <Users className="h-4 w-4" />
      case 'private': return <Lock className="h-4 w-4" />
    }
  }

  const getPrivacyLabel = () => {
    switch (privacy) {
      case 'public': return '公开'
      case 'friends': return '仅戒友'
      case 'private': return '仅自己'
    }
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span>发布动态</span>
          <div className="flex items-center gap-2">
            <Button
              size="sm"
              variant={isAnonymous ? "default" : "outline"}
              onClick={() => setIsAnonymous(!isAnonymous)}
            >
              {isAnonymous ? '匿名发布' : '实名发布'}
            </Button>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* 动态类型选择 */}
        <div className="space-y-2">
          <label className="text-sm font-medium text-gray-700">动态类型</label>
          <div className="flex flex-wrap gap-2">
            {postTypes.map((type) => (
              <button
                key={type.value}
                onClick={() => setPostType(type.value as any)}
                className={`px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                  postType === type.value
                    ? type.color
                    : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                }`}
              >
                <span className="mr-1">{type.icon}</span>
                {type.label}
              </button>
            ))}
          </div>
        </div>

        {/* 内容输入 */}
        <div className="space-y-2">
          <label className="text-sm font-medium text-gray-700">分享你的想法</label>
          <Textarea
            placeholder={
              postType === 'milestone' ? '分享你的里程碑成就...' :
              postType === 'question' ? '描述你遇到的问题...' :
              postType === 'support' ? '给戒友们一些鼓励...' :
              '今天有什么想分享的吗？'
            }
            value={content}
            onChange={(e) => setContent(e.target.value)}
            rows={4}
            className="resize-none"
          />
          <div className="flex justify-between text-xs text-gray-500">
            <span>{content.length}/500</span>
            <span>支持@提及用户和#话题标签</span>
          </div>
        </div>

        {/* 标签选择 */}
        <div className="space-y-2">
          <label className="text-sm font-medium text-gray-700">添加标签 (最多5个)</label>
          
          {/* 已选标签 */}
          {tags.length > 0 && (
            <div className="flex flex-wrap gap-2">
              {tags.map((tag) => (
                <Badge key={tag} variant="secondary" className="flex items-center gap-1">
                  #{tag}
                  <button
                    onClick={() => removeTag(tag)}
                    className="ml-1 hover:bg-gray-300 rounded-full p-0.5"
                  >
                    <X className="h-3 w-3" />
                  </button>
                </Badge>
              ))}
            </div>
          )}

          {/* 添加新标签 */}
          <div className="flex gap-2">
            <Input
              placeholder="输入标签名称"
              value={newTag}
              onChange={(e) => setNewTag(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && addTag(newTag)}
              className="flex-1"
            />
            <Button
              size="sm"
              variant="outline"
              onClick={() => addTag(newTag)}
              disabled={!newTag || tags.length >= 5}
            >
              <Plus className="h-4 w-4" />
            </Button>
          </div>

          {/* 预设标签 */}
          <div className="flex flex-wrap gap-2">
            {predefinedTags.map((tag) => (
              <button
                key={tag}
                onClick={() => addTag(tag)}
                disabled={tags.includes(tag) || tags.length >= 5}
                className={`px-2 py-1 text-xs rounded-full border transition-colors ${
                  tags.includes(tag) || tags.length >= 5
                    ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                    : 'bg-white text-gray-600 hover:bg-blue-50 hover:text-blue-600 hover:border-blue-300'
                }`}
              >
                #{tag}
              </button>
            ))}
          </div>
        </div>

        {/* 隐私设置 */}
        <div className="space-y-2">
          <label className="text-sm font-medium text-gray-700">隐私设置</label>
          <div className="flex gap-2">
            {[
              { value: 'public', label: '公开', desc: '所有人可见' },
              { value: 'friends', label: '仅戒友', desc: '仅戒友可见' },
              { value: 'private', label: '仅自己', desc: '仅自己可见' }
            ].map((option) => (
              <button
                key={option.value}
                onClick={() => setPrivacy(option.value as any)}
                className={`flex-1 p-3 rounded-lg border text-left transition-colors ${
                  privacy === option.value
                    ? 'border-blue-500 bg-blue-50'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
              >
                <div className="flex items-center gap-2 mb-1">
                  {option.value === 'public' && <Globe className="h-4 w-4" />}
                  {option.value === 'friends' && <Users className="h-4 w-4" />}
                  {option.value === 'private' && <Lock className="h-4 w-4" />}
                  <span className="font-medium text-sm">{option.label}</span>
                </div>
                <div className="text-xs text-gray-500">{option.desc}</div>
              </button>
            ))}
          </div>
        </div>

        {/* 操作按钮 */}
        <div className="flex items-center justify-between pt-4 border-t">
          <div className="flex items-center gap-2">
            <Button size="sm" variant="ghost" className="text-gray-500">
              <Image className="h-4 w-4 mr-1" />
              图片
            </Button>
            <Button size="sm" variant="ghost" className="text-gray-500">
              <Smile className="h-4 w-4 mr-1" />
              表情
            </Button>
            <Button size="sm" variant="ghost" className="text-gray-500">
              <Hash className="h-4 w-4 mr-1" />
              话题
            </Button>
          </div>

          <div className="flex items-center gap-2">
            {onCancel && (
              <Button variant="outline" onClick={onCancel}>
                取消
              </Button>
            )}
            <Button
              onClick={handleSubmit}
              disabled={!content.trim()}
              className="bg-blue-500 hover:bg-blue-600"
            >
              <Send className="h-4 w-4 mr-1" />
              发布
            </Button>
          </div>
        </div>

        {/* 发布预览 */}
        {content && (
          <div className="mt-4 p-3 bg-gray-50 rounded-lg border">
            <div className="text-xs text-gray-500 mb-2">发布预览：</div>
            <div className="flex items-center gap-2 mb-2">
              <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white text-sm">
                {isAnonymous ? '匿' : '我'}
              </div>
              <div>
                <div className="font-medium text-sm">{isAnonymous ? '匿名用户' : '当前用户'}</div>
                <div className="flex items-center gap-1 text-xs text-gray-500">
                  {getPrivacyIcon()}
                  <span>{getPrivacyLabel()}</span>
                  <span>·</span>
                  <span>刚刚</span>
                </div>
              </div>
            </div>
            <div className="text-sm text-gray-900 mb-2">{content}</div>
            {tags.length > 0 && (
              <div className="flex flex-wrap gap-1">
                {tags.map((tag) => (
                  <Badge key={tag} variant="outline" className="text-xs">
                    #{tag}
                  </Badge>
                ))}
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  )
}
