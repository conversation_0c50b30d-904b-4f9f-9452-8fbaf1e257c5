'use client'

import React, { Component, ErrorInfo, ReactNode } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { AlertTriangle, RefreshCw, Home, Bug } from 'lucide-react'

interface Props {
  children: ReactNode
  fallback?: ReactNode
}

interface State {
  hasError: boolean
  error?: Error
  errorInfo?: ErrorInfo
}

class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props)
    this.state = { hasError: false }
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error }
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    this.setState({
      error,
      errorInfo
    })
    
    // 记录错误到控制台
    console.error('ErrorBoundary caught an error:', error, errorInfo)
    
    // 这里可以添加错误上报逻辑
    // reportError(error, errorInfo)
  }

  handleRetry = () => {
    this.setState({ hasError: false, error: undefined, errorInfo: undefined })
  }

  handleGoHome = () => {
    window.location.href = '/dashboard'
  }

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback
      }

      return (
        <div className="min-h-screen flex items-center justify-center bg-gray-50 px-4">
          <Card className="max-w-md w-full">
            <CardHeader className="text-center">
              <div className="mx-auto w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mb-4">
                <AlertTriangle className="h-6 w-6 text-red-600" />
              </div>
              <CardTitle className="text-xl text-gray-900">出现了一些问题</CardTitle>
              <CardDescription>
                应用遇到了意外错误，我们正在努力修复
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* 错误详情（开发环境显示） */}
              {process.env.NODE_ENV === 'development' && this.state.error && (
                <div className="bg-gray-100 rounded-lg p-3">
                  <div className="flex items-center mb-2">
                    <Bug className="h-4 w-4 text-gray-600 mr-2" />
                    <span className="text-sm font-medium text-gray-900">错误详情</span>
                  </div>
                  <div className="text-xs text-gray-600 font-mono bg-white p-2 rounded border overflow-auto max-h-32">
                    <div className="mb-2">
                      <strong>错误信息:</strong> {this.state.error.message}
                    </div>
                    <div className="mb-2">
                      <strong>错误堆栈:</strong>
                      <pre className="whitespace-pre-wrap text-xs mt-1">
                        {this.state.error.stack}
                      </pre>
                    </div>
                    {this.state.errorInfo && (
                      <div>
                        <strong>组件堆栈:</strong>
                        <pre className="whitespace-pre-wrap text-xs mt-1">
                          {this.state.errorInfo.componentStack}
                        </pre>
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* 用户友好的错误信息 */}
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <h4 className="font-medium text-blue-900 mb-2">可能的解决方案：</h4>
                <ul className="text-sm text-blue-700 space-y-1">
                  <li>• 刷新页面重新加载</li>
                  <li>• 检查网络连接是否正常</li>
                  <li>• 清除浏览器缓存</li>
                  <li>• 如果问题持续，请联系技术支持</li>
                </ul>
              </div>

              {/* 操作按钮 */}
              <div className="flex gap-3">
                <Button 
                  onClick={this.handleRetry}
                  className="flex-1"
                >
                  <RefreshCw className="mr-2 h-4 w-4" />
                  重试
                </Button>
                <Button 
                  variant="outline"
                  onClick={this.handleGoHome}
                  className="flex-1"
                >
                  <Home className="mr-2 h-4 w-4" />
                  回到首页
                </Button>
              </div>

              {/* 联系支持 */}
              <div className="text-center pt-4 border-t">
                <p className="text-sm text-gray-600 mb-2">
                  如果问题持续存在，请联系我们
                </p>
                <div className="flex justify-center gap-4 text-xs text-gray-500">
                  <span>📧 <EMAIL></span>
                  <span>📱 400-123-4567</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )
    }

    return this.props.children
  }
}

export default ErrorBoundary

// 简化的错误边界组件，用于小组件
export function SimpleErrorBoundary({ 
  children, 
  fallback 
}: { 
  children: ReactNode
  fallback?: ReactNode 
}) {
  return (
    <ErrorBoundary 
      fallback={
        fallback || (
          <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
            <div className="flex items-center">
              <AlertTriangle className="h-5 w-5 text-red-500 mr-2" />
              <div>
                <h4 className="font-medium text-red-900">加载失败</h4>
                <p className="text-sm text-red-700">此组件暂时无法显示</p>
              </div>
            </div>
          </div>
        )
      }
    >
      {children}
    </ErrorBoundary>
  )
}

// Hook for error reporting
export function useErrorHandler() {
  const handleError = (error: Error, context?: string) => {
    console.error(`Error in ${context || 'unknown context'}:`, error)
    
    // 这里可以添加错误上报逻辑
    // reportError(error, { context })
    
    // 可以显示用户友好的错误提示
    // toast.error('操作失败，请稍后重试')
  }

  return { handleError }
}

// 错误上报函数（示例）
export function reportError(error: Error, context?: any) {
  // 在生产环境中，这里应该发送错误到监控服务
  if (process.env.NODE_ENV === 'production') {
    // 发送到错误监控服务，如 Sentry, LogRocket 等
    console.log('Reporting error to monitoring service:', {
      message: error.message,
      stack: error.stack,
      context,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href
    })
  }
}

// 性能监控Hook
export function usePerformanceMonitor(componentName: string) {
  React.useEffect(() => {
    const startTime = performance.now()
    
    return () => {
      const endTime = performance.now()
      const renderTime = endTime - startTime
      
      // 记录组件渲染时间
      if (renderTime > 100) { // 超过100ms记录
        console.warn(`Component ${componentName} took ${renderTime.toFixed(2)}ms to render`)
      }
    }
  }, [componentName])
}
