'use client'

import React from 'react'
import { Loader2, <PERSON>fresh<PERSON><PERSON>, <PERSON>ap, Heart, Target, Users } from 'lucide-react'
import { Card, CardContent } from '@/components/ui/card'

// 基础加载组件
export function LoadingSpinner({ 
  size = 'default', 
  className = '' 
}: { 
  size?: 'sm' | 'default' | 'lg'
  className?: string 
}) {
  const sizeClasses = {
    sm: 'h-4 w-4',
    default: 'h-6 w-6',
    lg: 'h-8 w-8'
  }

  return (
    <Loader2 className={`animate-spin ${sizeClasses[size]} ${className}`} />
  )
}

// 页面级加载组件
export function PageLoading({ message = '加载中...' }: { message?: string }) {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="text-center">
        <div className="relative">
          <div className="w-16 h-16 border-4 border-blue-200 border-t-blue-500 rounded-full animate-spin mx-auto mb-4"></div>
          <div className="absolute inset-0 flex items-center justify-center">
            <Heart className="h-6 w-6 text-blue-500 animate-pulse" />
          </div>
        </div>
        <p className="text-gray-600 text-lg font-medium">{message}</p>
        <p className="text-gray-500 text-sm mt-2">请稍候，正在为您准备内容...</p>
      </div>
    </div>
  )
}

// 卡片加载骨架
export function CardSkeleton({ 
  rows = 3, 
  showAvatar = false,
  className = '' 
}: { 
  rows?: number
  showAvatar?: boolean
  className?: string 
}) {
  return (
    <Card className={`animate-pulse ${className}`}>
      <CardContent className="p-6">
        <div className="space-y-4">
          {showAvatar && (
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gray-200 rounded-full"></div>
              <div className="space-y-2">
                <div className="h-4 bg-gray-200 rounded w-24"></div>
                <div className="h-3 bg-gray-200 rounded w-16"></div>
              </div>
            </div>
          )}
          
          <div className="space-y-3">
            {Array.from({ length: rows }).map((_, index) => (
              <div key={index} className="space-y-2">
                <div className="h-4 bg-gray-200 rounded w-full"></div>
                {index === rows - 1 && (
                  <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                )}
              </div>
            ))}
          </div>
          
          <div className="flex justify-between items-center pt-2">
            <div className="flex space-x-2">
              <div className="h-6 bg-gray-200 rounded w-12"></div>
              <div className="h-6 bg-gray-200 rounded w-12"></div>
            </div>
            <div className="h-8 bg-gray-200 rounded w-20"></div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

// 列表加载骨架
export function ListSkeleton({ 
  items = 5, 
  showAvatar = true 
}: { 
  items?: number
  showAvatar?: boolean 
}) {
  return (
    <div className="space-y-4">
      {Array.from({ length: items }).map((_, index) => (
        <div key={index} className="animate-pulse p-4 border rounded-lg">
          <div className="flex items-start space-x-3">
            {showAvatar && (
              <div className="w-8 h-8 bg-gray-200 rounded-full flex-shrink-0"></div>
            )}
            <div className="flex-1 space-y-2">
              <div className="h-4 bg-gray-200 rounded w-3/4"></div>
              <div className="h-3 bg-gray-200 rounded w-1/2"></div>
              <div className="flex space-x-4 mt-3">
                <div className="h-3 bg-gray-200 rounded w-12"></div>
                <div className="h-3 bg-gray-200 rounded w-12"></div>
                <div className="h-3 bg-gray-200 rounded w-16"></div>
              </div>
            </div>
          </div>
        </div>
      ))}
    </div>
  )
}

// 统计卡片加载骨架
export function StatCardSkeleton() {
  return (
    <Card className="animate-pulse">
      <CardContent className="p-6">
        <div className="flex items-center justify-between">
          <div className="space-y-2">
            <div className="h-4 bg-gray-200 rounded w-20"></div>
            <div className="h-8 bg-gray-200 rounded w-16"></div>
            <div className="h-3 bg-gray-200 rounded w-24"></div>
          </div>
          <div className="w-8 h-8 bg-gray-200 rounded"></div>
        </div>
      </CardContent>
    </Card>
  )
}

// 图表加载骨架
export function ChartSkeleton({ height = 'h-64' }: { height?: string }) {
  return (
    <Card className="animate-pulse">
      <CardContent className="p-6">
        <div className="space-y-4">
          <div className="flex justify-between items-center">
            <div className="h-5 bg-gray-200 rounded w-32"></div>
            <div className="h-4 bg-gray-200 rounded w-20"></div>
          </div>
          <div className={`bg-gray-200 rounded ${height} flex items-end justify-center space-x-2 p-4`}>
            {Array.from({ length: 7 }).map((_, index) => (
              <div 
                key={index}
                className="bg-gray-300 rounded-t w-8"
                style={{ height: `${Math.random() * 60 + 20}%` }}
              ></div>
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

// 内联加载组件
export function InlineLoading({ 
  text = '加载中', 
  size = 'sm' 
}: { 
  text?: string
  size?: 'sm' | 'default' 
}) {
  return (
    <div className="flex items-center justify-center py-4">
      <LoadingSpinner size={size} className="mr-2" />
      <span className={`text-gray-600 ${size === 'sm' ? 'text-sm' : 'text-base'}`}>
        {text}...
      </span>
    </div>
  )
}

// 按钮加载状态
export function ButtonLoading({ 
  loading, 
  children, 
  loadingText = '处理中...',
  ...props 
}: { 
  loading: boolean
  children: React.ReactNode
  loadingText?: string
  [key: string]: any 
}) {
  return (
    <button {...props} disabled={loading || props.disabled}>
      {loading ? (
        <div className="flex items-center">
          <LoadingSpinner size="sm" className="mr-2" />
          {loadingText}
        </div>
      ) : (
        children
      )}
    </button>
  )
}

// 主题化加载组件
export function ThemedLoading({ 
  theme = 'default',
  message = '加载中...' 
}: { 
  theme?: 'default' | 'success' | 'warning' | 'error'
  message?: string 
}) {
  const themes = {
    default: {
      bg: 'bg-blue-50',
      border: 'border-blue-200',
      text: 'text-blue-700',
      icon: 'text-blue-500',
      IconComponent: Zap
    },
    success: {
      bg: 'bg-green-50',
      border: 'border-green-200',
      text: 'text-green-700',
      icon: 'text-green-500',
      IconComponent: Target
    },
    warning: {
      bg: 'bg-yellow-50',
      border: 'border-yellow-200',
      text: 'text-yellow-700',
      icon: 'text-yellow-500',
      IconComponent: RefreshCw
    },
    error: {
      bg: 'bg-red-50',
      border: 'border-red-200',
      text: 'text-red-700',
      icon: 'text-red-500',
      IconComponent: Heart
    }
  }

  const currentTheme = themes[theme]
  const IconComponent = currentTheme.IconComponent

  return (
    <div className={`${currentTheme.bg} ${currentTheme.border} border rounded-lg p-6`}>
      <div className="flex items-center justify-center">
        <div className="relative">
          <LoadingSpinner className={`${currentTheme.icon} mr-3`} />
          <IconComponent className={`absolute inset-0 h-6 w-6 ${currentTheme.icon} animate-pulse`} />
        </div>
        <span className={`${currentTheme.text} font-medium`}>{message}</span>
      </div>
    </div>
  )
}

// 进度条加载
export function ProgressLoading({ 
  progress, 
  message = '加载中...',
  showPercentage = true 
}: { 
  progress: number
  message?: string
  showPercentage?: boolean 
}) {
  return (
    <div className="space-y-3">
      <div className="flex justify-between items-center">
        <span className="text-sm font-medium text-gray-700">{message}</span>
        {showPercentage && (
          <span className="text-sm text-gray-500">{Math.round(progress)}%</span>
        )}
      </div>
      <div className="w-full bg-gray-200 rounded-full h-2">
        <div 
          className="bg-blue-500 h-2 rounded-full transition-all duration-300 ease-out"
          style={{ width: `${Math.min(100, Math.max(0, progress))}%` }}
        ></div>
      </div>
    </div>
  )
}

// 空状态组件
export function EmptyState({ 
  icon: Icon = Users,
  title = '暂无数据',
  description = '当前没有可显示的内容',
  action
}: {
  icon?: React.ComponentType<any>
  title?: string
  description?: string
  action?: React.ReactNode
}) {
  return (
    <div className="text-center py-12">
      <div className="mx-auto w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
        <Icon className="h-8 w-8 text-gray-400" />
      </div>
      <h3 className="text-lg font-medium text-gray-900 mb-2">{title}</h3>
      <p className="text-gray-600 mb-6 max-w-sm mx-auto">{description}</p>
      {action}
    </div>
  )
}
