"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { mockApiResponse } from "@/lib/mock-data";

export default function CreatePostForm() {
  const router = useRouter();
  const [title, setTitle] = useState("");
  const [content, setContent] = useState("");
  const [type, setType] = useState("share");
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!title.trim() || !content.trim()) {
      alert("标题和内容不能为空");
      return;
    }

    setIsSubmitting(true);

    try {
      // 模拟API调用
      await mockApiResponse({ success: true, data: { title, content, type } });
      
      // 实际应用中，这里可能会触发一个全局状态更新或重新获取数据
      // 为了演示，我们只显示一个成功消息并跳转
      alert("动态发布成功！");
      router.push("/community");

    } catch (error) {
      console.error("发布失败:", error);
      alert("发布失败，请稍后再试。");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>创建你的帖子</CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="space-y-2">
            <label htmlFor="title" className="font-medium">标题</label>
            <Input
              id="title"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              placeholder="给你的帖子起个吸引人的标题"
              required
            />
          </div>

          <div className="space-y-2">
            <label htmlFor="content" className="font-medium">内容</label>
            <textarea
              id="content"
              value={content}
              onChange={(e) => setContent(e.target.value)}
              placeholder="分享你的经验、提出你的问题或给予他人支持..."
              required
              className="w-full p-2 border rounded-md min-h-[150px]"
            />
          </div>

          <div className="space-y-2">
            <label htmlFor="type" className="font-medium">帖子类型</label>
            <Select value={type} onValueChange={setType}>
              <SelectTrigger id="type">
                <SelectValue placeholder="选择帖子类型" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="share">经验分享</SelectItem>
                <SelectItem value="question">求助提问</SelectItem>
                <SelectItem value="support">相互支持</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <Button type="submit" disabled={isSubmitting} className="w-full">
            {isSubmitting ? "发布中..." : "确认发布"}
          </Button>
        </form>
      </CardContent>
    </Card>
  );
}
