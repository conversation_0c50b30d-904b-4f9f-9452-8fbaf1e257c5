"use client";

import { useState } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";

// 模拟的忏悔数据
const initialConfessions = [
  {
    id: 1,
    user: "匿名用户A",
    avatar: "https://github.com/shadcn.png",
    content: "我昨天熬夜打游戏了，没有按时睡觉，感觉很对不起自己。",
    timestamp: "2小时前",
  },
  {
    id: 2,
    user: "匿名用户B",
    avatar: "https://github.com/shadcn.png",
    content: "今天和家人吵架了，很后悔，希望可以和解。",
    timestamp: "5小时前",
  },
  {
    id: 3,
    user: "匿名用户C",
    avatar: "https://github.com/shadcn.png",
    content: "我最近总是拖延，很多计划都没有完成，要加油了！",
    timestamp: "1天前",
  },
];

export default function ConfessionWall() {
  const [confessions, setConfessions] = useState(initialConfessions);
  const [newConfession, setNewConfession] = useState("");

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (newConfession.trim() === "") return;

    const newEntry = {
      id: confessions.length + 1,
      user: "我",
      avatar: "https://github.com/shadcn.png",
      content: newConfession,
      timestamp: "刚刚",
    };

    setConfessions([newEntry, ...confessions]);
    setNewConfession("");
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>写下你的忏悔</CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            <Input
              value={newConfession}
              onChange={(e) => setNewConfession(e.target.value)}
              placeholder="在这里写下你的忏悔，释放心灵的重负..."
              
            />
            <Button type="submit">发布忏悔</Button>
          </form>
        </CardContent>
      </Card>

      <div className="space-y-4">
        <h2 className="text-2xl font-bold text-center">大家的忏悔</h2>
        {confessions.map((confession) => (
          <Card key={confession.id}>
            <CardContent className="p-4">
              <div className="flex items-start space-x-4">
                <Avatar>
                  <AvatarImage src={confession.avatar} alt={confession.user} />
                  <AvatarFallback>{confession.user.charAt(0)}</AvatarFallback>
                </Avatar>
                <div className="flex-1">
                  <div className="flex justify-between items-center">
                    <p className="font-semibold">{confession.user}</p>
                    <p className="text-xs text-gray-500">{confession.timestamp}</p>
                  </div>
                  <p className="mt-1 text-gray-700">{confession.content}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}
