'use client'

import { useState } from 'react'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import { Badge } from '@/components/ui/badge'
import { 
  Plus,
  Globe,
  Smartphone,
  Hash,
  X,
  AlertTriangle,
  Info
} from 'lucide-react'

interface BlockedItem {
  id: string
  type: 'website' | 'app' | 'keyword'
  name: string
  url?: string
  category?: string
  description?: string
  severity: 'low' | 'medium' | 'high'
  isActive: boolean
}

interface AddBlockItemDialogProps {
  onAdd: (item: Omit<BlockedItem, 'id'>) => void
}

const predefinedCategories = [
  { value: 'social', label: '社交媒体', examples: ['微博', 'Instagram', 'TikTok'] },
  { value: 'video', label: '视频平台', examples: ['YouTube', '抖音', 'B站'] },
  { value: 'gaming', label: '游戏娱乐', examples: ['王者荣耀', 'Steam', '原神'] },
  { value: 'news', label: '新闻资讯', examples: ['今日头条', '知乎', '微信'] },
  { value: 'shopping', label: '购物平台', examples: ['淘宝', '京东', '拼多多'] },
  { value: 'adult', label: '成人内容', examples: ['相关网站', '相关应用'] },
  { value: 'other', label: '其他', examples: [] }
]

const commonWebsites = [
  { name: 'Instagram', url: 'instagram.com', category: 'social', severity: 'medium' as const },
  { name: 'TikTok', url: 'tiktok.com', category: 'video', severity: 'high' as const },
  { name: '微博', url: 'weibo.com', category: 'social', severity: 'medium' as const },
  { name: 'YouTube', url: 'youtube.com', category: 'video', severity: 'medium' as const },
  { name: '抖音', url: 'douyin.com', category: 'video', severity: 'high' as const },
  { name: 'B站', url: 'bilibili.com', category: 'video', severity: 'medium' as const }
]

const commonApps = [
  { name: '抖音', category: 'video', severity: 'high' as const },
  { name: '快手', category: 'video', severity: 'high' as const },
  { name: '微博', category: 'social', severity: 'medium' as const },
  { name: '小红书', category: 'social', severity: 'medium' as const },
  { name: '王者荣耀', category: 'gaming', severity: 'medium' as const },
  { name: '和平精英', category: 'gaming', severity: 'medium' as const }
]

const commonKeywords = [
  { name: '色情', severity: 'high' as const },
  { name: '成人', severity: 'high' as const },
  { name: '诱惑', severity: 'medium' as const },
  { name: '刺激', severity: 'medium' as const }
]

export default function AddBlockItemDialog({ onAdd }: AddBlockItemDialogProps) {
  const [open, setOpen] = useState(false)
  const [type, setType] = useState<'website' | 'app' | 'keyword'>('website')
  const [name, setName] = useState('')
  const [url, setUrl] = useState('')
  const [category, setCategory] = useState('')
  const [description, setDescription] = useState('')
  const [severity, setSeverity] = useState<'low' | 'medium' | 'high'>('medium')
  const [showQuickAdd, setShowQuickAdd] = useState(true)

  const handleSubmit = () => {
    if (!name.trim()) return

    const newItem: Omit<BlockedItem, 'id'> = {
      type,
      name: name.trim(),
      url: type === 'website' ? url.trim() : undefined,
      category: category || undefined,
      description: description.trim() || undefined,
      severity,
      isActive: true
    }

    onAdd(newItem)
    handleReset()
    setOpen(false)
  }

  const handleReset = () => {
    setName('')
    setUrl('')
    setCategory('')
    setDescription('')
    setSeverity('medium')
  }

  const handleQuickAdd = (item: any) => {
    const newItem: Omit<BlockedItem, 'id'> = {
      type,
      name: item.name,
      url: item.url,
      category: item.category,
      severity: item.severity,
      isActive: true
    }

    onAdd(newItem)
  }

  const getQuickAddItems = () => {
    switch (type) {
      case 'website': return commonWebsites
      case 'app': return commonApps
      case 'keyword': return commonKeywords
      default: return []
    }
  }

  const getSeverityColor = (sev: string) => {
    switch (sev) {
      case 'high': return 'bg-red-100 text-red-800'
      case 'medium': return 'bg-yellow-100 text-yellow-800'
      case 'low': return 'bg-green-100 text-green-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getSeverityIcon = (sev: string) => {
    switch (sev) {
      case 'high': return <AlertTriangle className="h-3 w-3" />
      case 'medium': return <Info className="h-3 w-3" />
      case 'low': return <Info className="h-3 w-3" />
      default: return null
    }
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button>
          <Plus className="h-4 w-4 mr-2" />
          添加屏蔽项
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>添加屏蔽项</DialogTitle>
          <DialogDescription>
            选择要屏蔽的内容类型，帮助您远离诱惑
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* 类型选择 */}
          <div className="space-y-3">
            <Label>屏蔽类型</Label>
            <div className="grid grid-cols-3 gap-3">
              <button
                onClick={() => setType('website')}
                className={`p-4 rounded-lg border-2 transition-colors ${
                  type === 'website' 
                    ? 'border-blue-500 bg-blue-50' 
                    : 'border-gray-200 hover:border-gray-300'
                }`}
              >
                <Globe className="h-6 w-6 mx-auto mb-2 text-blue-500" />
                <div className="text-sm font-medium">网站</div>
                <div className="text-xs text-gray-500">屏蔽特定网站</div>
              </button>
              <button
                onClick={() => setType('app')}
                className={`p-4 rounded-lg border-2 transition-colors ${
                  type === 'app' 
                    ? 'border-blue-500 bg-blue-50' 
                    : 'border-gray-200 hover:border-gray-300'
                }`}
              >
                <Smartphone className="h-6 w-6 mx-auto mb-2 text-green-500" />
                <div className="text-sm font-medium">应用</div>
                <div className="text-xs text-gray-500">限制应用使用</div>
              </button>
              <button
                onClick={() => setType('keyword')}
                className={`p-4 rounded-lg border-2 transition-colors ${
                  type === 'keyword' 
                    ? 'border-blue-500 bg-blue-50' 
                    : 'border-gray-200 hover:border-gray-300'
                }`}
              >
                <Hash className="h-6 w-6 mx-auto mb-2 text-purple-500" />
                <div className="text-sm font-medium">关键词</div>
                <div className="text-xs text-gray-500">过滤敏感内容</div>
              </button>
            </div>
          </div>

          {/* 快速添加 */}
          {showQuickAdd && getQuickAddItems().length > 0 && (
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <Label>快速添加常见项目</Label>
                <Button 
                  variant="ghost" 
                  size="sm" 
                  onClick={() => setShowQuickAdd(false)}
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                {getQuickAddItems().map((item, index) => (
                  <button
                    key={index}
                    onClick={() => handleQuickAdd(item)}
                    className="p-3 text-left border rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    <div className="flex items-center justify-between mb-1">
                      <span className="font-medium text-sm">{item.name}</span>
                      <Badge className={getSeverityColor(item.severity)}>
                        {getSeverityIcon(item.severity)}
                        <span className="ml-1 text-xs">
                          {item.severity === 'high' ? '高' : item.severity === 'medium' ? '中' : '低'}
                        </span>
                      </Badge>
                    </div>
                    {'url' in item && (item as any).url && (
                      <div className="text-xs text-gray-500">{(item as any).url}</div>
                    )}
                  </button>
                ))}
              </div>
            </div>
          )}

          {/* 自定义添加 */}
          <div className="space-y-4">
            <Label>自定义添加</Label>
            
            <div className="space-y-3">
              <div>
                <Label htmlFor="name">名称 *</Label>
                <Input
                  id="name"
                  placeholder={`输入${type === 'website' ? '网站' : type === 'app' ? '应用' : '关键词'}名称`}
                  value={name}
                  onChange={(e) => setName(e.target.value)}
                />
              </div>

              {type === 'website' && (
                <div>
                  <Label htmlFor="url">网址</Label>
                  <Input
                    id="url"
                    placeholder="例如：example.com"
                    value={url}
                    onChange={(e) => setUrl(e.target.value)}
                  />
                </div>
              )}

              <div>
                <Label htmlFor="category">分类</Label>
                <Select value={category} onValueChange={setCategory}>
                  <SelectTrigger>
                    <SelectValue placeholder="选择分类（可选）" />
                  </SelectTrigger>
                  <SelectContent>
                    {predefinedCategories.map((cat) => (
                      <SelectItem key={cat.value} value={cat.value}>
                        {cat.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="severity">严重程度</Label>
                <Select value={severity} onValueChange={(value: 'low' | 'medium' | 'high') => setSeverity(value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="low">低 - 轻微影响</SelectItem>
                    <SelectItem value="medium">中 - 中等影响</SelectItem>
                    <SelectItem value="high">高 - 严重影响</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="description">描述</Label>
                <Textarea
                  id="description"
                  placeholder="添加备注说明（可选）"
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                  rows={2}
                />
              </div>
            </div>
          </div>

          {/* 操作按钮 */}
          <div className="flex justify-end space-x-3">
            <Button variant="outline" onClick={() => setOpen(false)}>
              取消
            </Button>
            <Button onClick={handleSubmit} disabled={!name.trim()}>
              添加屏蔽项
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
