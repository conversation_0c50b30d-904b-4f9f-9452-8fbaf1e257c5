'use client'

import { useState } from 'react'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import { Badge } from '@/components/ui/badge'
import { 
  Plus,
  User,
  Phone,
  Heart,
  Users,
  Briefcase,
  UserCheck,
  X
} from 'lucide-react'

interface EmergencyContact {
  id: string
  name: string
  phone: string
  relationship: string
  description?: string
  priority: 'high' | 'medium' | 'low'
  isAvailable24h: boolean
}

interface AddContactDialogProps {
  onAdd: (contact: Omit<EmergencyContact, 'id'>) => void
}

const relationshipTypes = [
  { value: 'family', label: '家人', icon: Heart, examples: ['父母', '兄弟姐妹', '配偶'] },
  { value: 'friend', label: '朋友', icon: Users, examples: ['好友', '同学', '室友'] },
  { value: 'professional', label: '专业人士', icon: Briefcase, examples: ['心理咨询师', '医生', '导师'] },
  { value: 'mentor', label: '导师/教练', icon: UserCheck, examples: ['戒色导师', '生活教练', '精神导师'] },
  { value: 'other', label: '其他', icon: User, examples: ['同事', '邻居'] }
]

const quickContacts = [
  { name: '心理咨询热线', phone: '************', relationship: 'professional', priority: 'high' as const, isAvailable24h: true, description: '24小时心理危机干预热线' },
  { name: '青少年心理热线', phone: '12355', relationship: 'professional', priority: 'high' as const, isAvailable24h: true, description: '共青团12355青少年服务台' },
  { name: '生命热线', phone: '************', relationship: 'professional', priority: 'high' as const, isAvailable24h: true, description: '北京生命热线' },
  { name: '上海心理热线', phone: '021-64389888', relationship: 'professional', priority: 'high' as const, isAvailable24h: true, description: '上海市心理援助热线' }
]

export default function AddContactDialog({ onAdd }: AddContactDialogProps) {
  const [open, setOpen] = useState(false)
  const [name, setName] = useState('')
  const [phone, setPhone] = useState('')
  const [relationship, setRelationship] = useState('')
  const [description, setDescription] = useState('')
  const [priority, setPriority] = useState<'high' | 'medium' | 'low'>('medium')
  const [isAvailable24h, setIsAvailable24h] = useState(false)
  const [showQuickAdd, setShowQuickAdd] = useState(true)

  const handleSubmit = () => {
    if (!name.trim() || !phone.trim()) return

    const newContact: Omit<EmergencyContact, 'id'> = {
      name: name.trim(),
      phone: phone.trim(),
      relationship: relationship || '朋友',
      description: description.trim() || undefined,
      priority,
      isAvailable24h
    }

    onAdd(newContact)
    handleReset()
    setOpen(false)
  }

  const handleReset = () => {
    setName('')
    setPhone('')
    setRelationship('')
    setDescription('')
    setPriority('medium')
    setIsAvailable24h(false)
  }

  const handleQuickAdd = (contact: any) => {
    const newContact: Omit<EmergencyContact, 'id'> = {
      name: contact.name,
      phone: contact.phone,
      relationship: contact.relationship,
      description: contact.description,
      priority: contact.priority,
      isAvailable24h: contact.isAvailable24h
    }

    onAdd(newContact)
  }

  const getPriorityColor = (prio: string) => {
    switch (prio) {
      case 'high': return 'bg-red-100 text-red-800'
      case 'medium': return 'bg-yellow-100 text-yellow-800'
      case 'low': return 'bg-green-100 text-green-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const formatPhoneNumber = (phone: string) => {
    // 简单的手机号格式化
    const cleaned = phone.replace(/\D/g, '')
    if (cleaned.length === 11) {
      return cleaned.replace(/(\d{3})(\d{4})(\d{4})/, '$1-$2-$3')
    }
    return phone
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button>
          <Plus className="h-4 w-4 mr-2" />
          添加联系人
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>添加紧急联系人</DialogTitle>
          <DialogDescription>
            添加可以在紧急情况下联系的人员，包括家人、朋友或专业人士
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* 快速添加专业热线 */}
          {showQuickAdd && (
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <Label>快速添加专业热线</Label>
                <Button 
                  variant="ghost" 
                  size="sm" 
                  onClick={() => setShowQuickAdd(false)}
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                {quickContacts.map((contact, index) => (
                  <button
                    key={index}
                    onClick={() => handleQuickAdd(contact)}
                    className="p-4 text-left border rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    <div className="flex items-center justify-between mb-2">
                      <span className="font-medium text-sm">{contact.name}</span>
                      <div className="flex items-center space-x-1">
                        <Badge className={getPriorityColor(contact.priority)}>
                          {contact.priority === 'high' ? '高' : contact.priority === 'medium' ? '中' : '低'}
                        </Badge>
                        {contact.isAvailable24h && (
                          <Badge variant="outline" className="text-xs">24h</Badge>
                        )}
                      </div>
                    </div>
                    <div className="text-sm text-blue-600 mb-1">{contact.phone}</div>
                    <div className="text-xs text-gray-500">{contact.description}</div>
                  </button>
                ))}
              </div>
            </div>
          )}

          {/* 自定义添加 */}
          <div className="space-y-4">
            <Label>自定义添加联系人</Label>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="name">姓名 *</Label>
                <Input
                  id="name"
                  placeholder="输入联系人姓名"
                  value={name}
                  onChange={(e) => setName(e.target.value)}
                />
              </div>

              <div>
                <Label htmlFor="phone">电话号码 *</Label>
                <Input
                  id="phone"
                  placeholder="输入电话号码"
                  value={phone}
                  onChange={(e) => setPhone(e.target.value)}
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="relationship">关系</Label>
                <Select value={relationship} onValueChange={setRelationship}>
                  <SelectTrigger>
                    <SelectValue placeholder="选择关系" />
                  </SelectTrigger>
                  <SelectContent>
                    {relationshipTypes.map((type) => (
                      <SelectItem key={type.value} value={type.value}>
                        <div className="flex items-center">
                          <type.icon className="h-4 w-4 mr-2" />
                          {type.label}
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="priority">优先级</Label>
                <Select value={priority} onValueChange={(value: 'high' | 'medium' | 'low') => setPriority(value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="high">高 - 首选联系人</SelectItem>
                    <SelectItem value="medium">中 - 一般联系人</SelectItem>
                    <SelectItem value="low">低 - 备用联系人</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div>
              <Label htmlFor="description">备注说明</Label>
              <Textarea
                id="description"
                placeholder="添加关于此联系人的备注信息（可选）"
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                rows={2}
              />
            </div>

            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="available24h"
                checked={isAvailable24h}
                onChange={(e) => setIsAvailable24h(e.target.checked)}
                className="rounded border-gray-300"
              />
              <Label htmlFor="available24h" className="text-sm">
                24小时可联系
              </Label>
            </div>
          </div>

          {/* 预览 */}
          {(name || phone) && (
            <div className="p-4 bg-gray-50 rounded-lg">
              <Label className="text-sm font-medium text-gray-700">预览</Label>
              <div className="mt-2 flex items-center justify-between">
                <div>
                  <div className="font-medium">{name || '未填写姓名'}</div>
                  <div className="text-sm text-gray-600">{formatPhoneNumber(phone) || '未填写电话'}</div>
                  {relationship && (
                    <div className="text-xs text-gray-500">
                      {relationshipTypes.find(t => t.value === relationship)?.label || relationship}
                    </div>
                  )}
                </div>
                <div className="flex items-center space-x-1">
                  <Badge className={getPriorityColor(priority)}>
                    {priority === 'high' ? '高' : priority === 'medium' ? '中' : '低'}
                  </Badge>
                  {isAvailable24h && (
                    <Badge variant="outline" className="text-xs">24h</Badge>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* 操作按钮 */}
          <div className="flex justify-end space-x-3">
            <Button variant="outline" onClick={() => setOpen(false)}>
              取消
            </Button>
            <Button onClick={handleSubmit} disabled={!name.trim() || !phone.trim()}>
              添加联系人
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
