'use client'

import { useState, useEffect, useRef } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Skeleton } from '@/components/ui/skeleton'
import { 
  Copy,
  Share,
  QrCode,
  Download,
  Users,
  Gift,
  Star,
  CheckCircle,
  RefreshCw,
  Link as LinkIcon,
  Smartphone
} from 'lucide-react'
import { mockApiResponse } from '@/lib/mock-data'

// 生成邀请码数据
function generateInviteCodeData() {
  // 生成6位字母数字邀请码，去掉易混淆字符 (0, O, I, 1, l)
  const chars = '23456789ABCDEFGHJKMNPQRSTUVWXYZ'
  let inviteCode = ''
  for (let i = 0; i < 6; i++) {
    inviteCode += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  const baseUrl = typeof window !== 'undefined' ? window.location.origin : 'https://app.example.com'
  
  return {
    user_invite_code: inviteCode,
    invite_link: `${baseUrl}/register?invite=${inviteCode}`,
    qr_code_url: `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${encodeURIComponent(`${baseUrl}/register?invite=${inviteCode}`)}`,
    statistics: {
      total_invites: 12,
      successful_invites: 8,
      pending_invites: 4,
      total_rewards: 240,
      this_month_invites: 3
    },
    recent_invites: [
      {
        id: 'invite-1',
        invited_user: '用户A',
        status: 'completed',
        reward: 30,
        created_at: '2024-01-15T10:30:00Z',
        completed_at: '2024-01-16T14:20:00Z'
      },
      {
        id: 'invite-2',
        invited_user: '用户B',
        status: 'completed',
        reward: 30,
        created_at: '2024-01-12T16:45:00Z',
        completed_at: '2024-01-13T09:15:00Z'
      },
      {
        id: 'invite-3',
        invited_user: '用户C',
        status: 'pending',
        reward: 0,
        created_at: '2024-01-10T20:10:00Z',
        completed_at: null
      }
    ],
    rewards: {
      per_invite: 30,
      bonus_milestones: [
        { count: 5, bonus: 50, achieved: true },
        { count: 10, bonus: 100, achieved: true },
        { count: 20, bonus: 200, achieved: false },
        { count: 50, bonus: 500, achieved: false }
      ]
    }
  }
}

const InviteCodeSkeleton = () => (
  <div className="space-y-6 animate-pulse">
    <Card>
      <CardHeader>
        <Skeleton className="h-6 w-32" />
        <Skeleton className="h-4 w-48 mt-2" />
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <Skeleton className="h-10 w-full" />
          <Skeleton className="h-10 w-full" />
          <Skeleton className="h-24 w-full" />
        </div>
        <div className="flex gap-2">
          <Skeleton className="h-10 w-full" />
          <Skeleton className="h-10 w-full" />
        </div>
      </CardContent>
    </Card>
    <Card>
      <CardHeader>
        <Skeleton className="h-6 w-24" />
        <Skeleton className="h-4 w-40 mt-2" />
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-2 md:grid-cols-5 gap-4 mb-6">
          {[...Array(5)].map((_, i) => <Skeleton key={i} className="h-20 w-full" />)}
        </div>
        <Skeleton className="h-5 w-32 mb-3" />
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
          {[...Array(4)].map((_, i) => <Skeleton key={i} className="h-12 w-full" />)}
        </div>
      </CardContent>
    </Card>
    <Card>
      <CardHeader>
        <Skeleton className="h-6 w-24" />
        <Skeleton className="h-4 w-32 mt-2" />
      </CardHeader>
      <CardContent className="space-y-3">
        {[...Array(3)].map((_, i) => <Skeleton key={i} className="h-16 w-full" />)}
      </CardContent>
    </Card>
  </div>
)

export default function InviteCodeManager() {
  const [inviteData, setInviteData] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const [copySuccess, setCopySuccess] = useState<string>('')
  const canvasRef = useRef<HTMLCanvasElement>(null)

  const loadInviteData = async () => {
    setLoading(true)
    try {
      const response = await mockApiResponse(generateInviteCodeData(), 1500) // Simulate 1.5s delay
      setInviteData(response.data)
    } catch (error) {
      console.error('加载邀请码数据失败:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    loadInviteData()
  }, [])

  const copyToClipboard = async (text: string, type: string) => {
    try {
      await navigator.clipboard.writeText(text)
      setCopySuccess(type)
      setTimeout(() => setCopySuccess(''), 2000)
    } catch (err) {
      console.error('复制失败:', err)
    }
  }

  const downloadQRCode = async () => {
    if (!inviteData?.qr_code_url) return
    
    try {
      const response = await fetch(inviteData.qr_code_url)
      const blob = await response.blob()
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `invite-qr-${inviteData.user_invite_code}.png`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      window.URL.revokeObjectURL(url)
    } catch (error) {
      console.error('下载二维码失败:', error)
    }
  }

  const shareInvite = async () => {
    if (!inviteData) return
    
    const shareData = {
      title: '加入戒色社区',
      text: `使用我的邀请码 ${inviteData.user_invite_code} 加入戒色社区，一起开始健康生活！`,
      url: inviteData.invite_link
    }

    try {
      if (navigator.share) {
        await navigator.share(shareData)
      } else {
        // 降级到复制链接
        await copyToClipboard(inviteData.invite_link, 'link')
      }
    } catch (error) {
      console.error('分享失败:', error)
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'completed':
        return <Badge className="bg-green-100 text-green-800">已完成</Badge>
      case 'pending':
        return <Badge variant="outline" className="text-yellow-600">待完成</Badge>
      default:
        return <Badge variant="secondary">未知</Badge>
    }
  }

  if (loading) {
    return <InviteCodeSkeleton />
  }

  return (
    <div className="space-y-6">
      {/* 邀请码展示 */}
      <Card className="relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-blue-500 to-purple-600 opacity-10"></div>
        <CardHeader className="relative">
          <CardTitle className="flex items-center">
            <Gift className="mr-2 h-5 w-5 text-blue-500" />
            我的邀请码
          </CardTitle>
          <CardDescription>
            分享邀请码，邀请朋友加入社区获得奖励
          </CardDescription>
        </CardHeader>
        <CardContent className="relative space-y-4">
          {/* 邀请码 */}
          <div className="flex items-center gap-3 p-4 bg-white/80 rounded-lg border">
            <div className="flex-1">
              <div className="text-sm text-gray-600 mb-1">邀请码</div>
              <div className="text-2xl font-bold text-blue-600 font-mono">
                {inviteData.user_invite_code}
              </div>
            </div>
            <Button
              size="sm"
              variant="outline"
              onClick={() => copyToClipboard(inviteData.user_invite_code, 'code')}
            >
              {copySuccess === 'code' ? (
                <CheckCircle className="h-4 w-4 text-green-500" />
              ) : (
                <Copy className="h-4 w-4" />
              )}
            </Button>
          </div>

          {/* 邀请链接 */}
          <div className="flex items-center gap-3 p-4 bg-white/80 rounded-lg border">
            <div className="flex-1">
              <div className="text-sm text-gray-600 mb-1">邀请链接</div>
              <div className="text-sm text-gray-900 break-all">
                {inviteData.invite_link}
              </div>
            </div>
            <Button
              size="sm"
              variant="outline"
              onClick={() => copyToClipboard(inviteData.invite_link, 'link')}
            >
              {copySuccess === 'link' ? (
                <CheckCircle className="h-4 w-4 text-green-500" />
              ) : (
                <LinkIcon className="h-4 w-4" />
              )}
            </Button>
          </div>

          {/* 二维码 */}
          <div className="flex items-center gap-4 p-4 bg-white/80 rounded-lg border">
            <div className="flex-1">
              <div className="text-sm text-gray-600 mb-2">邀请二维码</div>
              <div className="text-xs text-gray-500">扫码直接注册</div>
            </div>
            <div className="flex flex-col items-center gap-2">
              <img 
                src={inviteData.qr_code_url} 
                alt="邀请二维码"
                className="w-20 h-20 border rounded"
              />
              <Button size="sm" variant="outline" onClick={downloadQRCode}>
                <Download className="h-3 w-3 mr-1" />
                下载
              </Button>
            </div>
          </div>

          {/* 操作按钮 */}
          <div className="flex gap-2">
            <Button className="flex-1" onClick={shareInvite}>
              <Share className="mr-2 h-4 w-4" />
              分享邀请
            </Button>
            <Button variant="outline" onClick={() => copyToClipboard(inviteData.invite_link, 'link')}>
              <Smartphone className="mr-2 h-4 w-4" />
              复制链接
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* 邀请统计 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Users className="mr-2 h-5 w-5" />
            邀请统计
          </CardTitle>
          <CardDescription>
            查看您的邀请成果和获得的奖励
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-5 gap-4 mb-6">
            <div className="text-center p-3 bg-blue-50 rounded-lg">
              <div className="text-2xl font-bold text-blue-600">{inviteData.statistics.total_invites}</div>
              <div className="text-sm text-gray-600">总邀请数</div>
            </div>
            <div className="text-center p-3 bg-green-50 rounded-lg">
              <div className="text-2xl font-bold text-green-600">{inviteData.statistics.successful_invites}</div>
              <div className="text-sm text-gray-600">成功邀请</div>
            </div>
            <div className="text-center p-3 bg-yellow-50 rounded-lg">
              <div className="text-2xl font-bold text-yellow-600">{inviteData.statistics.pending_invites}</div>
              <div className="text-sm text-gray-600">待完成</div>
            </div>
            <div className="text-center p-3 bg-purple-50 rounded-lg">
              <div className="text-2xl font-bold text-purple-600">{inviteData.statistics.total_rewards}</div>
              <div className="text-sm text-gray-600">总奖励积分</div>
            </div>
            <div className="text-center p-3 bg-orange-50 rounded-lg">
              <div className="text-2xl font-bold text-orange-600">{inviteData.statistics.this_month_invites}</div>
              <div className="text-sm text-gray-600">本月邀请</div>
            </div>
          </div>

          {/* 奖励里程碑 */}
          <div className="space-y-3">
            <h4 className="font-medium text-gray-900">奖励里程碑</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              {inviteData.rewards.bonus_milestones.map((milestone: any, index: number) => (
                <div 
                  key={index}
                  className={`p-3 rounded-lg border ${
                    milestone.achieved 
                      ? 'bg-green-50 border-green-200' 
                      : 'bg-gray-50 border-gray-200'
                  }`}
                >
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="font-medium text-sm">
                        邀请 {milestone.count} 人
                      </div>
                      <div className="text-xs text-gray-600">
                        奖励 {milestone.bonus} 积分
                      </div>
                    </div>
                    {milestone.achieved ? (
                      <CheckCircle className="h-5 w-5 text-green-500" />
                    ) : (
                      <Star className="h-5 w-5 text-gray-400" />
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 最近邀请 */}
      <Card>
        <CardHeader>
          <CardTitle>最近邀请</CardTitle>
          <CardDescription>
            查看最近的邀请记录和状态
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {inviteData.recent_invites.map((invite: any) => (
              <div key={invite.id} className="flex items-center justify-between p-3 border rounded-lg">
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                    <Users className="h-4 w-4 text-blue-600" />
                  </div>
                  <div>
                    <div className="font-medium text-sm">{invite.invited_user}</div>
                    <div className="text-xs text-gray-500">
                      {new Date(invite.created_at).toLocaleDateString()}
                    </div>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  {getStatusBadge(invite.status)}
                  {invite.reward > 0 && (
                    <Badge variant="outline" className="text-green-600">
                      +{invite.reward} 积分
                    </Badge>
                  )}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}