/**
 * AI推荐引擎
 * 基于用户行为数据提供个性化内容推荐
 */

import { format, subDays, isAfter, isBefore } from 'date-fns'

// 用户行为类型
export type UserBehavior = {
  id: string
  user_id: string
  action_type: 'view' | 'like' | 'comment' | 'share' | 'complete' | 'skip'
  content_type: 'article' | 'video' | 'audio' | 'exercise' | 'challenge' | 'post'
  content_id: string
  content_category: string
  content_tags: string[]
  duration?: number // 浏览时长（秒）
  rating?: number // 用户评分 1-5
  timestamp: string
  context?: {
    device_type?: string
    time_of_day?: string
    mood_before?: number
    mood_after?: number
  }
}

// 内容项目
export type ContentItem = {
  id: string
  type: 'article' | 'video' | 'audio' | 'exercise' | 'challenge'
  title: string
  description: string
  category: string
  tags: string[]
  difficulty_level: 1 | 2 | 3 | 4 | 5
  estimated_duration: number // 分钟
  target_audience: string[]
  effectiveness_score: number // 0-100
  popularity_score: number // 0-100
  created_at: string
  updated_at: string
  metadata?: {
    author?: string
    source?: string
    language?: string
    format?: string
  }
}

// 用户画像
export type UserProfile = {
  user_id: string
  demographics: {
    age_range?: string
    experience_level: 'beginner' | 'intermediate' | 'advanced'
    goals: string[]
    challenges: string[]
  }
  preferences: {
    content_types: string[]
    categories: string[]
    difficulty_preference: number
    session_duration_preference: number
  }
  behavior_patterns: {
    active_hours: number[]
    preferred_days: number[]
    engagement_rate: number
    completion_rate: number
    avg_session_duration: number
  }
  current_state: {
    streak_days: number
    mood_trend: number
    energy_level: number
    motivation_level: number
    last_active: string
  }
}

// 推荐结果
export type RecommendationResult = {
  content_id: string
  score: number
  reasons: string[]
  category: string
  priority: 'high' | 'medium' | 'low'
  timing_suggestion?: string
  personalization_factors: {
    behavior_match: number
    preference_match: number
    context_match: number
    novelty_score: number
  }
}

/**
 * AI推荐引擎类
 */
export class RecommendationEngine {
  private userBehaviors: Map<string, UserBehavior[]> = new Map()
  private contentItems: Map<string, ContentItem> = new Map()
  private userProfiles: Map<string, UserProfile> = new Map()

  /**
   * 添加用户行为数据
   */
  addUserBehavior(behavior: UserBehavior) {
    const userId = behavior.user_id
    if (!this.userBehaviors.has(userId)) {
      this.userBehaviors.set(userId, [])
    }
    this.userBehaviors.get(userId)!.push(behavior)
  }

  /**
   * 添加内容项目
   */
  addContentItem(content: ContentItem) {
    this.contentItems.set(content.id, content)
  }

  /**
   * 更新用户画像
   */
  updateUserProfile(profile: UserProfile) {
    this.userProfiles.set(profile.user_id, profile)
  }

  /**
   * 生成用户画像
   */
  generateUserProfile(userId: string): UserProfile {
    const behaviors = this.userBehaviors.get(userId) || []
    const recentBehaviors = behaviors.filter(b => 
      isAfter(new Date(b.timestamp), subDays(new Date(), 30))
    )

    // 分析内容偏好
    const contentTypePrefs = this.analyzeContentTypePreferences(recentBehaviors)
    const categoryPrefs = this.analyzeCategoryPreferences(recentBehaviors)
    const difficultyPref = this.analyzeDifficultyPreference(recentBehaviors)

    // 分析行为模式
    const activeHours = this.analyzeActiveHours(recentBehaviors)
    const engagementRate = this.calculateEngagementRate(recentBehaviors)
    const completionRate = this.calculateCompletionRate(recentBehaviors)

    const profile: UserProfile = {
      user_id: userId,
      demographics: {
        experience_level: this.inferExperienceLevel(behaviors),
        goals: this.inferUserGoals(behaviors),
        challenges: this.inferUserChallenges(behaviors)
      },
      preferences: {
        content_types: contentTypePrefs,
        categories: categoryPrefs,
        difficulty_preference: difficultyPref,
        session_duration_preference: this.calculateAvgSessionDuration(recentBehaviors)
      },
      behavior_patterns: {
        active_hours: activeHours,
        preferred_days: this.analyzePreferredDays(recentBehaviors),
        engagement_rate: engagementRate,
        completion_rate: completionRate,
        avg_session_duration: this.calculateAvgSessionDuration(recentBehaviors)
      },
      current_state: {
        streak_days: 0, // 需要从其他数据源获取
        mood_trend: this.analyzeMoodTrend(recentBehaviors),
        energy_level: 3, // 默认值
        motivation_level: this.analyzeMotivationLevel(recentBehaviors),
        last_active: recentBehaviors[recentBehaviors.length - 1]?.timestamp || new Date().toISOString()
      }
    }

    this.userProfiles.set(userId, profile)
    return profile
  }

  /**
   * 获取个性化推荐
   */
  getRecommendations(
    userId: string, 
    limit: number = 10,
    context?: {
      current_mood?: number
      available_time?: number
      device_type?: string
    }
  ): RecommendationResult[] {
    let profile = this.userProfiles.get(userId)
    if (!profile) {
      profile = this.generateUserProfile(userId)
    }

    const candidates = Array.from(this.contentItems.values())
    const recommendations: RecommendationResult[] = []

    for (const content of candidates) {
      const score = this.calculateRecommendationScore(content, profile, context)
      const reasons = this.generateRecommendationReasons(content, profile, score)
      
      recommendations.push({
        content_id: content.id,
        score: score.total,
        reasons,
        category: content.category,
        priority: score.total > 0.8 ? 'high' : score.total > 0.6 ? 'medium' : 'low',
        timing_suggestion: this.suggestOptimalTiming(content, profile),
        personalization_factors: {
          behavior_match: score.behavior_match,
          preference_match: score.preference_match,
          context_match: score.context_match,
          novelty_score: score.novelty_score
        }
      })
    }

    // 排序并返回前N个
    return recommendations
      .sort((a, b) => b.score - a.score)
      .slice(0, limit)
  }

  /**
   * 计算推荐分数
   */
  private calculateRecommendationScore(
    content: ContentItem,
    profile: UserProfile,
    context?: any
  ) {
    // 行为匹配分数 (40%)
    const behaviorMatch = this.calculateBehaviorMatch(content, profile)
    
    // 偏好匹配分数 (30%)
    const preferenceMatch = this.calculatePreferenceMatch(content, profile)
    
    // 上下文匹配分数 (20%)
    const contextMatch = this.calculateContextMatch(content, profile, context)
    
    // 新颖性分数 (10%)
    const noveltyScore = this.calculateNoveltyScore(content, profile)

    const total = (
      behaviorMatch * 0.4 +
      preferenceMatch * 0.3 +
      contextMatch * 0.2 +
      noveltyScore * 0.1
    )

    return {
      total,
      behavior_match: behaviorMatch,
      preference_match: preferenceMatch,
      context_match: contextMatch,
      novelty_score: noveltyScore
    }
  }

  /**
   * 分析内容类型偏好
   */
  private analyzeContentTypePreferences(behaviors: UserBehavior[]): string[] {
    const typeCount = new Map<string, number>()
    
    behaviors.forEach(b => {
      if (b.action_type === 'view' || b.action_type === 'like' || b.action_type === 'complete') {
        typeCount.set(b.content_type, (typeCount.get(b.content_type) || 0) + 1)
      }
    })

    return Array.from(typeCount.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 3)
      .map(([type]) => type)
  }

  /**
   * 分析类别偏好
   */
  private analyzeCategoryPreferences(behaviors: UserBehavior[]): string[] {
    const categoryCount = new Map<string, number>()
    
    behaviors.forEach(b => {
      if (b.action_type === 'view' || b.action_type === 'like' || b.action_type === 'complete') {
        categoryCount.set(b.content_category, (categoryCount.get(b.content_category) || 0) + 1)
      }
    })

    return Array.from(categoryCount.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 5)
      .map(([category]) => category)
  }

  /**
   * 分析活跃时间
   */
  private analyzeActiveHours(behaviors: UserBehavior[]): number[] {
    const hourCount = new Map<number, number>()
    
    behaviors.forEach(b => {
      const hour = new Date(b.timestamp).getHours()
      hourCount.set(hour, (hourCount.get(hour) || 0) + 1)
    })

    return Array.from(hourCount.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 6)
      .map(([hour]) => hour)
  }

  /**
   * 计算参与度
   */
  private calculateEngagementRate(behaviors: UserBehavior[]): number {
    if (behaviors.length === 0) return 0
    
    const engagedActions = behaviors.filter(b => 
      b.action_type === 'like' || 
      b.action_type === 'comment' || 
      b.action_type === 'share' ||
      b.action_type === 'complete'
    ).length

    return engagedActions / behaviors.length
  }

  /**
   * 计算完成率
   */
  private calculateCompletionRate(behaviors: UserBehavior[]): number {
    const completableActions = behaviors.filter(b => 
      b.content_type === 'exercise' || 
      b.content_type === 'challenge' ||
      b.content_type === 'article'
    )
    
    if (completableActions.length === 0) return 0
    
    const completedActions = completableActions.filter(b => b.action_type === 'complete')
    return completedActions.length / completableActions.length
  }

  /**
   * 其他辅助方法的占位符实现
   */
  private analyzeDifficultyPreference(behaviors: UserBehavior[]): number { return 3 }
  private analyzePreferredDays(behaviors: UserBehavior[]): number[] { return [1, 2, 3, 4, 5] }
  private inferExperienceLevel(behaviors: UserBehavior[]): 'beginner' | 'intermediate' | 'advanced' { return 'intermediate' }
  private inferUserGoals(behaviors: UserBehavior[]): string[] { return ['戒色', '健康生活'] }
  private inferUserChallenges(behaviors: UserBehavior[]): string[] { return ['压力管理', '时间管理'] }
  private calculateAvgSessionDuration(behaviors: UserBehavior[]): number { return 15 }
  private analyzeMoodTrend(behaviors: UserBehavior[]): number { return 3 }
  private analyzeMotivationLevel(behaviors: UserBehavior[]): number { return 4 }
  private calculateBehaviorMatch(content: ContentItem, profile: UserProfile): number { return Math.random() * 0.8 + 0.2 }
  private calculatePreferenceMatch(content: ContentItem, profile: UserProfile): number { return Math.random() * 0.8 + 0.2 }
  private calculateContextMatch(content: ContentItem, profile: UserProfile, context?: any): number { return Math.random() * 0.8 + 0.2 }
  private calculateNoveltyScore(content: ContentItem, profile: UserProfile): number { return Math.random() * 0.8 + 0.2 }
  private generateRecommendationReasons(content: ContentItem, profile: UserProfile, score: any): string[] {
    return ['基于您的浏览历史', '符合您的兴趣偏好', '适合当前时间']
  }
  private suggestOptimalTiming(content: ContentItem, profile: UserProfile): string {
    return '建议在晚上8-10点阅读'
  }
}

// 导出单例实例
export const recommendationEngine = new RecommendationEngine()
